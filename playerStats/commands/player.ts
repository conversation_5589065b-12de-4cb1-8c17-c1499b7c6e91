import { Message, EmbedBuilder } from 'discord.js';
import { BattlemetricsAPI } from '../services/battlemetrics.js';
import { SteamAPI } from '../services/steam.js';
import { PlayerStats, IPlayerStats } from '../models/PlayerStats.js';

interface Config {
  emojis?: {
    battlemetrics?: string;
    rust?: string;
    oasis?: string;
    stats?: string;
    steam?: string;
  };
}

export class StatsCommand {
  private battlemetricsAPI: BattlemetricsAPI;
  private steamAPI: SteamAPI;
  private config: Config;

  constructor(battlemetricsAPI: BattlemetricsAPI, steamAPI: SteamAPI, config: Config = {}) {
    this.battlemetricsAPI = battlemetricsAPI;
    this.steamAPI = steamAPI;
    this.config = config;
  }

  async execute(message: Message, args: string[]): Promise<void> {
  if (args.length === 0) {
    await message.reply('Please provide a Steam ID or player name. Usage: `.player <steamid>` or `.player <playername>`');
    return;
  }

  const identifier = args.join(' ').trim();

  // Check if it's a Steam ID (17 digits) or player name
  const isSteamId = /^\d{17}$/.test(identifier);

  if (!isSteamId) {
    console.log(`Input "${identifier}" is not a Steam ID, treating as player name`);
  }

  // Send initial loading message
  const loadingMessage = await message.reply('Fetching player information...');

  try {
    // Step 1: Get comprehensive stats
    console.log(`Getting player stats for: ${identifier} (${isSteamId ? 'Steam ID' : 'Player Name'})`);

    const bmStats = await this.battlemetricsAPI.getPlayerStats(identifier);

    if (!bmStats) {
      await loadingMessage.edit(`"${identifier}" not found on Battlemetrics. Player may not have played on tracked servers.`);
      return;
    }

    console.log(`Found player: "${bmStats.playerName}" with ${bmStats.totalHours.toFixed(1)} total hours`);

    // Step 1.5: Get Battlemetrics first seen date
    let bmFirstSeenDate: Date | null = null;
    try {
      const sessions = await this.battlemetricsAPI.getPlayerSessions(bmStats.playerId);
      if (sessions.length > 0) {
        const earliestSession = sessions.reduce((earliest, session) => {
          const sessionStart = new Date(session.attributes.start);
          const earliestStart = new Date(earliest.attributes.start);
          return sessionStart < earliestStart ? session : earliest;
        });
        bmFirstSeenDate = new Date(earliestSession.attributes.start);
        console.log(`Battlemetrics first seen: ${bmFirstSeenDate.toDateString()}`);
      }
    } catch (error) {
      console.log('Error getting Battlemetrics first seen date:', error);
    }

    // Step 1.6: Check MongoDB for kills/deaths data (if we have Steam ID)
    let mongoStats: IPlayerStats | null = null;
    if (isSteamId) {
      try {
        mongoStats = await PlayerStats.findOne({ steamId: identifier });
        if (mongoStats) {
          console.log(`Found MongoDB stats: ${mongoStats.kills} kills, ${mongoStats.deaths} deaths, K/D: ${mongoStats.killDeathRatio}`);
        } else {
          console.log('No MongoDB stats found for this player');
        }
      } catch (error) {
        console.log('Error checking MongoDB:', error);
      }
    }

    let steamPlayer: any = null;
    let steamCreatedDate: Date | null = null;
    if (isSteamId) {
      try {
        steamPlayer = await this.steamAPI.findPlayerBySteamId(identifier);
        console.log(`Steam profile found: ${steamPlayer?.personaname || 'Unknown'}`);

        if (steamPlayer?.timecreated) {
          steamCreatedDate = new Date(steamPlayer.timecreated * 1000);
          console.log(`Steam account created: ${steamCreatedDate.toDateString()}`);
        }
      } catch (error) {
        console.log('Steam profile lookup failed, continuing without avatar');
      }
    }

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(0x00AE86)
      .setTimestamp()
      .setFooter({ text: `Oasis Player Checker • Today at ${new Date().toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true })}` });

    // Add Steam avatar if available
    if (steamPlayer?.avatarfull) {
      embed.setThumbnail(steamPlayer.avatarfull);
    }

    // Get configurable emojis
    const battlemetricsEmoji = this.config.emojis?.battlemetrics || '';
    const rustEmoji = this.config.emojis?.rust || '';
    const oasisEmoji = this.config.emojis?.oasis || '';
    const statsEmoji = this.config.emojis?.stats || '';
    const steamEmoji = this.config.emojis?.steam || '';

    // Build the main description
    let description = `**${bmStats.playerName}**\n`;
    // Battlemetrics link
    description += `[${identifier}](https://steamcommunity.com/profiles/${identifier}/) ${battlemetricsEmoji} [Battlemetrics](https://www.battlemetrics.com/players/${bmStats.playerId})\n\n`;

    // Add server information directly in the description
    if (bmStats.orgServers.length > 0) {
      bmStats.orgServers.forEach(server => {
        const emoji = this.getServerEmoji(server.name);
        description += `${emoji} **${server.name}** \`${Math.round(server.hours)} Hours\`\n`;
      });
      description += `\n`;
    }

    // Add Rust total hours and Oasis hours inline
    const totalOrgHours = bmStats.orgServers.reduce((total, server) => total + server.hours, 0);
    description += `${rustEmoji} **Rust** \`${Math.round(bmStats.totalHours)} Hours\`     ${oasisEmoji} **Oasis** \`${Math.round(totalOrgHours)} Hours\`\n\n`;

    // Add account creation dates if available
    if (steamCreatedDate || bmFirstSeenDate) {
      description += `**Account Created:**\n`;
      if (steamCreatedDate) {
        const steamTimestamp = Math.floor(steamCreatedDate.getTime() / 1000);
        description += `${steamEmoji} <t:${steamTimestamp}:D>\n`;
      }
      if (bmFirstSeenDate) {
        const bmTimestamp = Math.floor(bmFirstSeenDate.getTime() / 1000);
        description += `${battlemetricsEmoji} <t:${bmTimestamp}:D>\n`;
      }
      description += `\n`;
    }

    // Add PVP Statistics
    const kills = mongoStats?.kills ?? bmStats.kills;
    const deaths = mongoStats?.deaths ?? bmStats.deaths;
    const kdRatio = mongoStats?.killDeathRatio ?? bmStats.killDeathRatio;

    description += `**Oasis PVP Stats (14d):** ${statsEmoji}\n`;
    description += `**Kills:** ${kills}\n`;
    description += `**Deaths:** ${deaths}\n`;
    description += `**K/D:** ${kdRatio.toFixed(2)}`;

    embed.setDescription(description);

    await loadingMessage.edit({ content: '', embeds: [embed] });

  } catch (error) {
    console.error('Error in stats command:', error);

    await loadingMessage.edit('An error occurred while fetching player statistics. Please try again later.');
  }
}

  private formatRecentActivity(activities: any[]): string {
    if (activities.length === 0) {
      return 'No recent activity found';
    }

    return activities.map(activity => {
      const timestamp = new Date(activity.attributes.timestamp);
      const timeAgo = this.getTimeAgo(timestamp);

      if (activity.type === 'rustKill') {
        const victim = activity.attributes.data.victim?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `Killed **${victim}** with ${weapon} *${timeAgo}*`;
      } else if (activity.type === 'rustDeath') {
        const killer = activity.attributes.data.killer?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `Killed by **${killer}** with ${weapon} *${timeAgo}*`;
      }

      return `Unknown activity *${timeAgo}*`;
    }).join('\n');
  }

  private getServerEmoji(serverName: string): string {
    // Map server names to appropriate region flags
    const serverEmojis: { [key: string]: string } = {
      // NA servers
      'US 5x': '🇺🇸',
      'US 10x': '🇺🇸',
      'US Main': '🇺🇸',
      'US PvP': '🇺🇸',
      'Oasis Rust Main': '🇺🇸',
      'Oasis US': '🇺🇸',
      'NA 2x Monthly': '🇺🇸',
      'NA 2x Main': '🇺🇸',

      // EU servers
      'EU 5x': '🇪🇺',
      'EU 10x': '🇪🇺',
      'EU Main': '🇪🇺',
      'EU PvP': '🇪🇺',
      'Oasis EU': '🇪🇺',

      // AU servers
      'AU 5x': '🇦🇺',
      'AU 10x': '🇦🇺',
      'AU Main': '🇦🇺',
      'AU PvP': '🇦🇺',
      'Oasis AU': '🇦🇺'
    };

    // Check for exact matches first
    if (serverEmojis[serverName]) {
      return serverEmojis[serverName];
    }

    // Check for partial matches based on region
    const lowerName = serverName.toLowerCase();

    if (lowerName.includes('us') || lowerName.includes('na') || lowerName.includes('america')) return '🇺🇸';
    if (lowerName.includes('eu') || lowerName.includes('europe')) return '🇪🇺';
    if (lowerName.includes('au') || lowerName.includes('australia') || lowerName.includes('oceania')) return '🇦🇺';

    // Default to NA for unknown servers
    return '🇺🇸';
  }

  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }
}
