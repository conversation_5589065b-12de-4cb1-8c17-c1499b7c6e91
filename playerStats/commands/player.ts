import { Message, EmbedBuilder } from 'discord.js';
import { BattlemetricsAPI } from '../services/battlemetrics.js';
import { SteamAPI } from '../services/steam.js';
import { PlayerStats, IPlayerStats } from '../models/PlayerStats.js';

interface Config {
  emojis?: {
    battlemetrics?: string;
    rust?: string;
    oasis?: string;
  };
}

export class StatsCommand {
  private battlemetricsAPI: BattlemetricsAPI;
  private steamAPI: SteamAPI;
  private config: Config;

  constructor(battlemetricsAPI: BattlemetricsAPI, steamAPI: SteamAPI, config: Config = {}) {
    this.battlemetricsAPI = battlemetricsAPI;
    this.steamAPI = steamAPI;
    this.config = config;
  }

  async execute(message: Message, args: string[]): Promise<void> {
    if (args.length === 0) {
      await message.reply('Please provide a Steam ID or player name. Usage: `.player <steamid>` or `.player <playername>`');
      return;
    }

    const identifier = args.join(' ').trim();

    // Check if it's a Steam ID (17 digits) or player name
    const isSteamId = /^\d{17}$/.test(identifier);

    if (!isSteamId) {
      console.log(`Input "${identifier}" is not a Steam ID, treating as player name`);
    }

    // Send initial loading message
    const loadingMessage = await message.reply('Fetching player information from Steam...');

    try {
      // Step 1: Get comprehensive stats
      console.log(`Getting player stats for: ${identifier} (${isSteamId ? 'Steam ID' : 'Player Name'})`);
      await loadingMessage.edit(isSteamId ? 'Searching Battlemetrics by Steam ID...' : 'Searching Battlemetrics by player name...');

      const bmStats = await this.battlemetricsAPI.getPlayerStats(identifier);

      if (!bmStats) {
        await loadingMessage.edit(`"${identifier}" not found on Battlemetrics. Player may not have played on tracked servers.`);
        return;
      }

      console.log(`Found player: "${bmStats.playerName}" with ${bmStats.totalHours.toFixed(1)} total hours`);

      // Step 1.5: Check MongoDB for kills/deaths data (if we have Steam ID)
      let mongoStats: IPlayerStats | null = null;
      if (isSteamId) {
        try {
          await loadingMessage.edit(`Found player: ${bmStats.playerName}. Checking database for K/D stats...`);
          mongoStats = await PlayerStats.findOne({ steamId: identifier });
          if (mongoStats) {
            console.log(`Found MongoDB stats: ${mongoStats.kills} kills, ${mongoStats.deaths} deaths, K/D: ${mongoStats.killDeathRatio}`);
          } else {
            console.log('No MongoDB stats found for this player');
          }
        } catch (error) {
          console.log('Error checking MongoDB:', error);
        }
      }

      // Step 2: Get Steam profile info for avatar (optional, only if we have Steam ID)
      await loadingMessage.edit(`Found player: ${bmStats.playerName}. Getting Steam profile...`);

      let steamPlayer: any = null;
      if (isSteamId) {
        try {
          steamPlayer = await this.steamAPI.findPlayerBySteamId(identifier);
          console.log(`Steam profile found: ${steamPlayer?.personaname || 'Unknown'}`);
        } catch (error) {
          console.log('Steam profile lookup failed, continuing without avatar');
        }
      }

      // Create embed with compact layout like the second screenshot
      const embed = new EmbedBuilder()
        .setColor(0x00AE86)
        .setTimestamp()
        .setFooter({ text: 'Oasis Profile Viewer' });

      // Add Steam avatar if available
      if (steamPlayer?.avatarfull) {
        embed.setThumbnail(steamPlayer.avatarfull);
      }

      // Get configurable emojis
      const battlemetricsEmoji = this.config.emojis?.battlemetrics || '🔍';
      const rustEmoji = this.config.emojis?.rust || '🦀';
      const oasisEmoji = this.config.emojis?.oasis || '';

      // Build the main description with server grid layout
      let description = `**${bmStats.playerName}**\n`;
      description += `${identifier} ${battlemetricsEmoji} Battlemetrics\n\n`;

      // Add servers in a grid format with emojis
      if (bmStats.orgServers.length > 0) {
        // Group servers in rows of 3 for better layout
        for (let i = 0; i < bmStats.orgServers.length; i += 3) {
          const serverRow = bmStats.orgServers.slice(i, i + 3);

          // Server names row
          const nameRow = serverRow.map(server => {
            const emoji = this.getServerEmoji(server.name);
            return `${emoji} **${server.name}**`;
          }).join('     ');

          // Server hours row
          const hoursRow = serverRow.map(server =>
            `**${Math.round(server.hours)} Hours**`
          ).join('     ');

          description += `${nameRow}\n${hoursRow}\n\n`;
        }
      }

      // Add Rust total hours
      description += `${rustEmoji} **Rust**\n**${Math.round(bmStats.totalHours)} Hours**\n\n`;

      // Calculate and add total org hours
      const totalOrgHours = bmStats.orgServers.reduce((total, server) => total + server.hours, 0);
      description += `${} **Oasis**\n**${Math.round(totalOrgHours)} Hours**\n\n`;

      // Add PVP Statistics - use MongoDB data if available, otherwise fallback to Battlemetrics
      const kills = mongoStats?.kills ?? bmStats.kills;
      const deaths = mongoStats?.deaths ?? bmStats.deaths;
      const kdRatio = mongoStats?.killDeathRatio ?? bmStats.killDeathRatio;

      description += `**Oasis PVP Statistics 14D** <:stats:1376065633506299954>\n`;
      description += `**Ratio:** ${kdRatio.toFixed(1)}\n`;
      description += `**Kills:** ${kills}\n`;
      description += `**Deaths:** ${deaths}`;

      embed.setDescription(description);

      // Add recent activity if available
      if (bmStats.recentActivity.length > 0) {
        const recentActivityText = this.formatRecentActivity(bmStats.recentActivity.slice(0, 5));
        embed.addFields({
          name: 'Recent Activity (Last 5)',
          value: recentActivityText,
          inline: false
        });
      }

      await loadingMessage.edit({ content: '', embeds: [embed] });

    } catch (error) {
      console.error('Error in stats command:', error);

      await loadingMessage.edit('An error occurred while fetching player statistics. Please try again later.');
    }
  }

  private formatRecentActivity(activities: any[]): string {
    if (activities.length === 0) {
      return 'No recent activity found';
    }

    return activities.map(activity => {
      const timestamp = new Date(activity.attributes.timestamp);
      const timeAgo = this.getTimeAgo(timestamp);

      if (activity.type === 'rustKill') {
        const victim = activity.attributes.data.victim?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `Killed **${victim}** with ${weapon} *${timeAgo}*`;
      } else if (activity.type === 'rustDeath') {
        const killer = activity.attributes.data.killer?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `Killed by **${killer}** with ${weapon} *${timeAgo}*`;
      }

      return `Unknown activity *${timeAgo}*`;
    }).join('\n');
  }

  private getServerEmoji(serverName: string): string {
    // Map server names to appropriate emojis/flags
    const serverEmojis: { [key: string]: string } = {
      // US servers
      'US 5x': '🇺🇸',
      'US 10x': '🇺🇸',
      'US Main': '🇺🇸',
      'US PvP': '🇺🇸',
      'Oasis Rust Main': '🇺🇸',
      'Oasis US': '🇺🇸',

      // EU servers
      'EU 5x': '🇪🇺',
      'EU 10x': '🇪🇺',
      'EU Main': '🇪🇺',
      'EU PvP': '🇪🇺',
      'Oasis EU': '🇪🇺',

      // Special servers
      '2x Monthly': '📅',
      '2x Medium': '⚡',
      '2x Quad': '🔥',
      'Monthly': '📅',
      'Medium': '⚡',
      'Quad': '🔥',
      'PvE': '🛡️',
      'Creative': '🎨',
      'Build': '🔨',
      'Modded': '🔧'
    };

    // Check for exact matches first
    if (serverEmojis[serverName]) {
      return serverEmojis[serverName];
    }

    // Check for partial matches
    const lowerName = serverName.toLowerCase();

    if (lowerName.includes('us') || lowerName.includes('america')) return '🇺🇸';
    if (lowerName.includes('eu') || lowerName.includes('europe')) return '🇪🇺';
    if (lowerName.includes('monthly')) return '📅';
    if (lowerName.includes('medium')) return '⚡';
    if (lowerName.includes('quad')) return '🔥';
    if (lowerName.includes('2x')) return '⚡';
    if (lowerName.includes('5x')) return '🚀';
    if (lowerName.includes('10x')) return '🚀';
    if (lowerName.includes('pve')) return '🛡️';
    if (lowerName.includes('creative') || lowerName.includes('build')) return '🎨';
    if (lowerName.includes('modded')) return '🔧';

    // Default emoji for unknown servers
    return '🖥️';
  }

  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }
}
