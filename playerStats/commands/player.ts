import { Message, EmbedBuilder } from 'discord.js';
import { BattlemetricsAPI } from '../services/battlemetrics.js';
import { SteamAPI } from '../services/steam.js';

export class StatsCommand {
  private battlemetricsAPI: BattlemetricsAPI;
  private steamAPI: SteamAPI;

  constructor(battlemetricsAPI: BattlemetricsAPI, steamAPI: SteamAPI) {
    this.battlemetricsAPI = battlemetricsAPI;
    this.steamAPI = steamAPI;
  }

  async execute(message: Message, args: string[]): Promise<void> {
    if (args.length === 0) {
      await message.reply('Please provide a Steam ID. Usage: `.player <steamid>`');
      return;
    }

    const steamId = args.join(' ').trim();

    // Validate Steam ID format
    if (!/^\d{17}$/.test(steamId)) {
      await message.reply('Please provide a valid 17-digit Steam ID.');
      return;
    }

    // Send initial loading message
    const loadingMessage = await message.reply('Fetching player information from Steam...');

    try {
      // Step 1: Get player info from Steam API
      console.log(`Looking up Steam ID: ${steamId}`);
      const steamPlayer = await this.steamAPI.findPlayerBySteamId(steamId);

      if (!steamPlayer) {
        console.log(`Steam lookup failed for ID: ${steamId}`);
        await loadingMessage.edit(`Steam ID "${steamId}" not found or profile is private.`);
        return;
      }

      console.log(`Steam API returned:`, {
        steamid: steamPlayer.steamid,
        personaname: steamPlayer.personaname,
        profileurl: steamPlayer.profileurl
      });

      await loadingMessage.edit(`Found Steam player: "${steamPlayer.personaname}". Searching Battlemetrics...`);

      // Step 2: Search Battlemetrics using the Steam player name
      console.log(`Searching Battlemetrics for: "${steamPlayer.personaname}"`);
      const bmStats = await this.battlemetricsAPI.getPlayerStats(steamPlayer.personaname);

      if (!bmStats) {
        console.log(`Battlemetrics search failed for: "${steamPlayer.personaname}"`);
        await loadingMessage.edit(`Steam player "${steamPlayer.personaname}" not found on Battlemetrics. They may not have played on tracked servers.`);
        return;
      }

      console.log(`Battlemetrics found player: "${bmStats.playerName}" (ID: ${bmStats.playerId})`);
      console.log(`Total hours: ${bmStats.totalHours}, Org servers: ${bmStats.orgServers.length}`);

      // Create embed
      const embed = new EmbedBuilder()
        .setTitle(`${bmStats.playerName}`)
        .setThumbnail(steamPlayer.avatarfull)
        .setColor(0x00AE86)
        .setTimestamp()
        .setFooter({ text: 'Data from Steam & Battlemetrics' });

      // Add total hours field
      embed.addFields({
        name: 'Total Hours',
        value: `${bmStats.totalHours.toFixed(1)} hours`,
        inline: true
      });

      // Add K/D stats
      embed.addFields({
        name: 'Combat (14 Days)',
        value: [
          `**Kills:** ${bmStats.kills}`,
          `**Deaths:** ${bmStats.deaths}`,
          `**K/D:** ${bmStats.killDeathRatio.toFixed(2)}`
        ].join('\n'),
        inline: true
      });

      // Add empty field for spacing
      embed.addFields({
        name: '\u200b',
        value: '\u200b',
        inline: true
      });

      // Add server hours as individual inline fields
      if (bmStats.orgServers.length > 0) {
        for (const server of bmStats.orgServers) {
          embed.addFields({
            name: `${server.name}`,
            value: `${server.hours.toFixed(1)} hours`,
            inline: true
          });
        }
      } else {
        embed.addFields({
          name: 'Organization Servers',
          value: 'No hours recorded',
          inline: true
        });
      }

      // Add recent activity if available
      if (bmStats.recentActivity.length > 0) {
        const recentActivityText = this.formatRecentActivity(bmStats.recentActivity.slice(0, 5));
        embed.addFields({
          name: 'Recent Activity (Last 5)',
          value: recentActivityText,
          inline: false
        });
      }

      await loadingMessage.edit({ content: '', embeds: [embed] });

    } catch (error) {
      console.error('Error in stats command:', error);

      await loadingMessage.edit('An error occurred while fetching player statistics. Please try again later.');
    }
  }

  private formatRecentActivity(activities: any[]): string {
    if (activities.length === 0) {
      return 'No recent activity found';
    }

    return activities.map(activity => {
      const timestamp = new Date(activity.attributes.timestamp);
      const timeAgo = this.getTimeAgo(timestamp);

      if (activity.type === 'rustKill') {
        const victim = activity.attributes.data.victim?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `Killed **${victim}** with ${weapon} *${timeAgo}*`;
      } else if (activity.type === 'rustDeath') {
        const killer = activity.attributes.data.killer?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `Killed by **${killer}** with ${weapon} *${timeAgo}*`;
      }

      return `Unknown activity *${timeAgo}*`;
    }).join('\n');
  }

  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }
}
