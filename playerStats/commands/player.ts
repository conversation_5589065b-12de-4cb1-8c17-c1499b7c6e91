import { Message, EmbedBuilder } from 'discord.js';
import { BattlemetricsAPI } from '../services/battlemetrics.js';
import { SteamAPI } from '../services/steam.js';

export class StatsCommand {
  private battlemetricsAPI: BattlemetricsAPI;
  private steamAPI: SteamAPI;

  constructor(battlemetricsAPI: BattlemetricsAPI, steamAPI: SteamAPI) {
    this.battlemetricsAPI = battlemetricsAPI;
    this.steamAPI = steamAPI;
  }

  async execute(message: Message, args: string[]): Promise<void> {
    if (args.length === 0) {
      await message.reply('Please provide a Steam ID or player name. Usage: `.player <steamid>` or `.player <playername>`');
      return;
    }

    const identifier = args.join(' ').trim();

    // Check if it's a Steam ID (17 digits) or player name
    const isSteamId = /^\d{17}$/.test(identifier);

    if (!isSteamId) {
      console.log(`Input "${identifier}" is not a Steam ID, treating as player name`);
    }

    // Send initial loading message
    const loadingMessage = await message.reply('Fetching player information from Steam...');

    try {
      // Step 1: Get comprehensive stats
      console.log(`Getting player stats for: ${identifier} (${isSteamId ? 'Steam ID' : 'Player Name'})`);
      await loadingMessage.edit(isSteamId ? 'Searching Battlemetrics by Steam ID...' : 'Searching Battlemetrics by player name...');

      const bmStats = await this.battlemetricsAPI.getPlayerStats(identifier);

      if (!bmStats) {
        await loadingMessage.edit(`"${identifier}" not found on Battlemetrics. Player may not have played on tracked servers.`);
        return;
      }

      console.log(`Found player: "${bmStats.playerName}" with ${bmStats.totalHours.toFixed(1)} total hours`);

      // Step 2: Get Steam profile info for avatar (optional, only if we have Steam ID)
      await loadingMessage.edit(`Found player: ${bmStats.playerName}. Getting Steam profile...`);

      let steamPlayer: any = null;
      if (isSteamId) {
        try {
          steamPlayer = await this.steamAPI.findPlayerBySteamId(identifier);
          console.log(`Steam profile found: ${steamPlayer?.personaname || 'Unknown'}`);
        } catch (error) {
          console.log('Steam profile lookup failed, continuing without avatar');
        }
      }

      // Create embed
      const embed = new EmbedBuilder()
        .setTitle(`${bmStats.playerName}`)
        .setColor(0x00AE86)
        .setTimestamp()
        .setFooter({ text: 'Oasis Profile Checker' });

      // Add Steam avatar if available
      if (steamPlayer?.avatarfull) {
        embed.setThumbnail(steamPlayer.avatarfull);
      }

      // 1. Add server hours first (individual servers with custom names)
      if (bmStats.orgServers.length > 0) {
        for (const server of bmStats.orgServers) {
          embed.addFields({
            name: `${server.name}`,
            value: `${server.hours.toFixed(1)} hours`,
            inline: true
          });
        }
      } else {
        embed.addFields({
          name: 'Organization Servers',
          value: 'No hours recorded',
          inline: true
        });
      }

      // 2. Add Rust hours (total hours from all servers)
      embed.addFields({
        name: 'Rust Hours',
        value: `${bmStats.totalHours.toFixed(1)} hours`,
        inline: true
      });

      // 3. Add total organization hours (sum of org servers)
      const totalOrgHours = bmStats.orgServers.reduce((total, server) => total + server.hours, 0);
      embed.addFields({
        name: 'Total Org Hours',
        value: `${totalOrgHours.toFixed(1)} hours`,
        inline: true
      });

      // Add K/D stats
      embed.addFields({
        name: 'Oasis PVP Statistics (14 Days)',
        value: [
          `**Kills:** ${bmStats.kills}`,
          `**Deaths:** ${bmStats.deaths}`,
          `**K/D:** ${bmStats.killDeathRatio.toFixed(2)}`
        ].join('\n'),
        inline: true
      });

      // Add empty field for spacing
      embed.addFields({
        name: '\u200b',
        value: '\u200b',
        inline: true
      });

      // Add recent activity if available
      if (bmStats.recentActivity.length > 0) {
        const recentActivityText = this.formatRecentActivity(bmStats.recentActivity.slice(0, 5));
        embed.addFields({
          name: 'Recent Activity (Last 5)',
          value: recentActivityText,
          inline: false
        });
      }

      await loadingMessage.edit({ content: '', embeds: [embed] });

    } catch (error) {
      console.error('Error in stats command:', error);

      await loadingMessage.edit('An error occurred while fetching player statistics. Please try again later.');
    }
  }

  private formatRecentActivity(activities: any[]): string {
    if (activities.length === 0) {
      return 'No recent activity found';
    }

    return activities.map(activity => {
      const timestamp = new Date(activity.attributes.timestamp);
      const timeAgo = this.getTimeAgo(timestamp);

      if (activity.type === 'rustKill') {
        const victim = activity.attributes.data.victim?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `Killed **${victim}** with ${weapon} *${timeAgo}*`;
      } else if (activity.type === 'rustDeath') {
        const killer = activity.attributes.data.killer?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `Killed by **${killer}** with ${weapon} *${timeAgo}*`;
      }

      return `Unknown activity *${timeAgo}*`;
    }).join('\n');
  }

  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }
}
