import { <PERSON>lash<PERSON><PERSON>mandB<PERSON>er, Em<PERSON><PERSON><PERSON>er, ChatInputCommandInteraction } from 'discord.js';
import { BattlemetricsAPI } from '../services/battlemetrics.js';
import { ServerInfoService } from '../services/serverInfo.js';

export class PlayerStatsCommand {
  public data: any;
  private battlemetricsAPI: BattlemetricsAPI;
  private serverInfoService: ServerInfoService;

  constructor(battlemetricsAPI: BattlemetricsAPI, serverInfoService: ServerInfoService) {
    this.battlemetricsAPI = battlemetricsAPI;
    this.serverInfoService = serverInfoService;

    this.data = new SlashCommandBuilder()
      .setName('playerstats')
      .setDescription('Get player statistics from Battlemetrics')
      .addStringOption(option =>
        option
          .setName('player')
          .setDescription('Player name to search for')
          .setRequired(true)
      );
  }

  async execute(interaction: ChatInputCommandInteraction): Promise<void> {
    const playerName = interaction.options.getString('player', true);

    await interaction.deferReply();

    try {
      const stats = await this.battlemetricsAPI.getPlayerStats(playerName);

      if (!stats) {
        await interaction.editReply({
          content: `❌ Player "${playerName}" not found on Battlemetrics.`
        });
        return;
      }

      const embed = new EmbedBuilder()
        .setTitle(`📊 Player Statistics: ${stats.playerName}`)
        .setColor(0x00AE86)
        .setTimestamp()
        .setFooter({ text: 'Data from Battlemetrics' });

      // Add total hours
      embed.addFields({
        name: '⏰ Total Hours',
        value: `${stats.totalHours.toFixed(1)} hours`,
        inline: true
      });

      // Add K/D stats
      embed.addFields({
        name: '⚔️ Combat Stats (Last 14 Days)',
        value: [
          `**Kills:** ${stats.kills}`,
          `**Deaths:** ${stats.deaths}`,
          `**K/D Ratio:** ${stats.killDeathRatio.toFixed(2)}`
        ].join('\n'),
        inline: true
      });

      // Add server hours breakdown
      const serverHoursText = await this.formatServerHours(stats.serverHours);
      if (serverHoursText) {
        embed.addFields({
          name: '🖥️ Server Hours',
          value: serverHoursText,
          inline: false
        });
      }

      // Add recent activity if available
      if (stats.recentActivity.length > 0) {
        const recentActivityText = this.formatRecentActivity(stats.recentActivity.slice(0, 5));
        embed.addFields({
          name: '📋 Recent Activity (Last 5)',
          value: recentActivityText,
          inline: false
        });
      }

      await interaction.editReply({ embeds: [embed] });

    } catch (error) {
      console.error('Error in playerstats command:', error);

      await interaction.editReply({
        content: '❌ An error occurred while fetching player statistics. Please try again later.'
      });
    }
  }

  private async formatServerHours(serverHours: { [serverId: string]: number }): Promise<string> {
    const entries: string[] = [];

    for (const [serverId, hours] of Object.entries(serverHours)) {
      if (hours > 0) {
        try {
          const serverInfo = await this.serverInfoService.getServerInfo(serverId);
          const serverName = serverInfo?.name || `Server ${serverId}`;
          const playerCount = serverInfo ? `(${serverInfo.players}/${serverInfo.maxPlayers})` : '';
          entries.push(`**${serverName}** ${playerCount}: ${hours.toFixed(1)}h`);
        } catch (error) {
          entries.push(`**Server ${serverId}:** ${hours.toFixed(1)}h`);
        }
      }
    }

    return entries.length > 0 ? entries.join('\n') : 'No hours recorded on tracked servers';
  }

  private formatRecentActivity(activities: any[]): string {
    if (activities.length === 0) {
      return 'No recent activity found';
    }

    return activities.map(activity => {
      const timestamp = new Date(activity.attributes.timestamp);
      const timeAgo = this.getTimeAgo(timestamp);

      if (activity.type === 'rustKill') {
        const victim = activity.attributes.data.victim?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `🔫 Killed **${victim}** with ${weapon} *${timeAgo}*`;
      } else if (activity.type === 'rustDeath') {
        const killer = activity.attributes.data.killer?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `💀 Killed by **${killer}** with ${weapon} *${timeAgo}*`;
      }

      return `❓ Unknown activity *${timeAgo}*`;
    }).join('\n');
  }

  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }
}
