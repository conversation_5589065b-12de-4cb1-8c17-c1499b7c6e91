import { Message, EmbedBuilder } from 'discord.js';
import { BattlemetricsAPI } from '../services/battlemetrics.js';
import { SteamAPI } from '../services/steam.js';

export class StatsCommand {
  private battlemetricsAPI: BattlemetricsAPI;
  private steamAPI: SteamAPI;

  constructor(battlemetricsAPI: BattlemetricsAPI, steamAPI: SteamAPI) {
    this.battlemetricsAPI = battlemetricsAPI;
    this.steamAPI = steamAPI;
  }

  async execute(message: Message, args: string[]): Promise<void> {
    if (args.length === 0) {
      await message.reply('❌ Please provide a Steam ID or profile URL. Usage: `!stats <steamid/profile>`');
      return;
    }

    const playerIdentifier = args.join(' ').trim();
    
    // Send initial loading message
    const loadingMessage = await message.reply('🔄 Fetching player statistics...');

    try {
      // Get Steam info first
      const steamInfo = await this.steamAPI.getPlayerInfo(playerIdentifier);
      
      if (!steamInfo) {
        await loadingMessage.edit('❌ Player not found on Steam or Rust not owned.');
        return;
      }

      // Get Battlemetrics stats using Steam name
      const bmStats = await this.battlemetricsAPI.getPlayerStats(
        steamInfo.player.personaname, 
        steamInfo.rustHours
      );

      if (!bmStats) {
        await loadingMessage.edit(`❌ Player "${steamInfo.player.personaname}" not found on Battlemetrics.`);
        return;
      }

      // Create embed
      const embed = new EmbedBuilder()
        .setTitle(`📊 ${bmStats.playerName}`)
        .setThumbnail(steamInfo.player.avatarfull)
        .setColor(0x00AE86)
        .setTimestamp()
        .setFooter({ text: 'Data from Steam & Battlemetrics' });

      // Add Steam hours field
      embed.addFields({
        name: '🎮 Steam Hours',
        value: `${steamInfo.rustHours.toFixed(1)} hours`,
        inline: true
      });

      // Add K/D stats
      embed.addFields({
        name: '⚔️ Combat (14 Days)',
        value: [
          `**Kills:** ${bmStats.kills}`,
          `**Deaths:** ${bmStats.deaths}`,
          `**K/D:** ${bmStats.killDeathRatio.toFixed(2)}`
        ].join('\n'),
        inline: true
      });

      // Add empty field for spacing
      embed.addFields({
        name: '\u200b',
        value: '\u200b',
        inline: true
      });

      // Add server hours as individual inline fields
      if (bmStats.orgServers.length > 0) {
        for (const server of bmStats.orgServers) {
          embed.addFields({
            name: `🖥️ ${server.name}`,
            value: `${server.hours.toFixed(1)} hours`,
            inline: true
          });
        }
      } else {
        embed.addFields({
          name: '🖥️ Organization Servers',
          value: 'No hours recorded',
          inline: true
        });
      }

      // Add recent activity if available
      if (bmStats.recentActivity.length > 0) {
        const recentActivityText = this.formatRecentActivity(bmStats.recentActivity.slice(0, 5));
        embed.addFields({
          name: '📋 Recent Activity (Last 5)',
          value: recentActivityText,
          inline: false
        });
      }

      await loadingMessage.edit({ content: '', embeds: [embed] });

    } catch (error) {
      console.error('Error in stats command:', error);
      
      await loadingMessage.edit('❌ An error occurred while fetching player statistics. Please try again later.');
    }
  }

  private formatRecentActivity(activities: any[]): string {
    if (activities.length === 0) {
      return 'No recent activity found';
    }

    return activities.map(activity => {
      const timestamp = new Date(activity.attributes.timestamp);
      const timeAgo = this.getTimeAgo(timestamp);
      
      if (activity.type === 'rustKill') {
        const victim = activity.attributes.data.victim?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `🔫 Killed **${victim}** with ${weapon} *${timeAgo}*`;
      } else if (activity.type === 'rustDeath') {
        const killer = activity.attributes.data.killer?.name || 'Unknown';
        const weapon = activity.attributes.data.weapon || 'Unknown weapon';
        return `💀 Killed by **${killer}** with ${weapon} *${timeAgo}*`;
      }
      
      return `❓ Unknown activity *${timeAgo}*`;
    }).join('\n');
  }

  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }
}
