import mongoose, { Schema, Document } from 'mongoose';

export interface IServerInfo {
  id: string;
  name: string;
  hours: number;
}

export interface IActivityLogEntry {
  id: string;
  type: string;
  timestamp: string;
  data: {
    killer?: {
      name: string;
      id: string;
    };
    victim?: {
      name: string;
      id: string;
    };
    weapon?: string;
    distance?: number;
  };
}

export interface IPlayerStats extends Document {
  steamId: string;
  playerId: string;
  playerName: string;
  totalHours: number;
  orgServers: IServerInfo[];
  kills: number;
  deaths: number;
  killDeathRatio: number;
  recentActivity: IActivityLogEntry[];
  lastUpdated: Date;
  createdAt: Date;
}

const ServerInfoSchema = new Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  hours: { type: Number, required: true, default: 0 }
}, { _id: false });

const ActivityLogEntrySchema = new Schema({
  id: { type: String, required: true },
  type: { type: String, required: true },
  timestamp: { type: String, required: true },
  data: {
    killer: {
      name: String,
      id: String
    },
    victim: {
      name: String,
      id: String
    },
    weapon: String,
    distance: Number
  }
}, { _id: false });

const PlayerStatsSchema = new Schema({
  steamId: { 
    type: String, 
    required: true, 
    unique: true,
    index: true 
  },
  playerId: { 
    type: String, 
    required: true 
  },
  playerName: { 
    type: String, 
    required: true 
  },
  totalHours: { 
    type: Number, 
    required: true, 
    default: 0 
  },
  orgServers: [ServerInfoSchema],
  kills: { 
    type: Number, 
    required: true, 
    default: 0 
  },
  deaths: { 
    type: Number, 
    required: true, 
    default: 0 
  },
  killDeathRatio: { 
    type: Number, 
    required: true, 
    default: 0 
  },
  recentActivity: [ActivityLogEntrySchema],
  lastUpdated: { 
    type: Date, 
    default: Date.now 
  }
}, {
  timestamps: true
});

// Pre-save middleware to calculate K/D ratio
PlayerStatsSchema.pre('save', function(next) {
  if (this.deaths > 0) {
    this.killDeathRatio = Number((this.kills / this.deaths).toFixed(2));
  } else {
    this.killDeathRatio = this.kills;
  }
  this.lastUpdated = new Date();
  next();
});

// Index for efficient queries
PlayerStatsSchema.index({ steamId: 1 });
PlayerStatsSchema.index({ playerName: 1 });
PlayerStatsSchema.index({ lastUpdated: -1 });

export const PlayerStats = mongoose.model<IPlayerStats>('PlayerStats', PlayerStatsSchema);
