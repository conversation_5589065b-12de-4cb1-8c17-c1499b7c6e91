import mongoose, { Schema, Document } from 'mongoose';

export interface IServerInfo {
  id: string;
  name: string;
  hours: number;
}

export interface IActivityLogEntry {
  id: string;
  type: string;
  timestamp: string;
  data: {
    killer?: {
      name: string;
      id: string;
    };
    victim?: {
      name: string;
      id: string;
    };
    weapon?: string;
    distance?: number;
    resourceType?: string;
    amount?: number;
  };
}

export interface IDailyStats {
  date: string; // YYYY-MM-DD format
  kills: number;
  deaths: number;
  rocketsShot: number;
  c4Thrown: number;
  headshots: number;
  resourcesGathered: Record<string, number>;
  structuresBuilt: number;
  itemsCrafted: number;
  explosivesUsed: number;
  animalsKilled: number;
  barrelsLooted: number;
  playtimeMinutes: number;
  serverSessions: Array<{
    serverId: string;
    serverName: string;
    minutes: number;
  }>;
  activities: IActivityLogEntry[];
}

export interface IPlayerStats extends Document {
  steamId: string;
  playerId: string;
  playerName: string;
  totalHours: number;
  orgServers: IServerInfo[];
  kills: number;
  deaths: number;
  killDeathRatio: number;
  rocketsShot: number;
  c4Thrown: number;
  headshots: number;
  resourcesGathered: Record<string, number>;
  structuresBuilt: number;
  itemsCrafted: number;
  explosivesUsed: number;
  animalsKilled: number;
  barrelsLooted: number;
  recentActivity: IActivityLogEntry[];
  dailyStats: IDailyStats[];
  lastUpdated: Date;
  createdAt: Date;
}

const ServerInfoSchema = new Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  hours: { type: Number, required: true, default: 0 }
}, { _id: false });

const ActivityLogEntrySchema = new Schema({
  id: { type: String, required: true },
  type: { type: String, required: true },
  timestamp: { type: String, required: true },
  data: {
    killer: {
      name: String,
      id: String
    },
    victim: {
      name: String,
      id: String
    },
    weapon: String,
    distance: Number,
    resourceType: String,
    amount: Number
  }
}, { _id: false });

const DailyStatsSchema = new Schema({
  date: { type: String, required: true },
  kills: { type: Number, required: true, default: 0 },
  deaths: { type: Number, required: true, default: 0 },
  rocketsShot: { type: Number, required: true, default: 0 },
  c4Thrown: { type: Number, required: true, default: 0 },
  headshots: { type: Number, required: true, default: 0 },
  resourcesGathered: { type: Map, of: Number, default: {} },
  structuresBuilt: { type: Number, required: true, default: 0 },
  itemsCrafted: { type: Number, required: true, default: 0 },
  explosivesUsed: { type: Number, required: true, default: 0 },
  animalsKilled: { type: Number, required: true, default: 0 },
  barrelsLooted: { type: Number, required: true, default: 0 },
  playtimeMinutes: { type: Number, required: true, default: 0 },
  serverSessions: [{
    serverId: { type: String, required: true },
    serverName: { type: String, required: true },
    minutes: { type: Number, required: true, default: 0 }
  }],
  activities: [ActivityLogEntrySchema]
}, { _id: false });

const PlayerStatsSchema = new Schema({
  steamId: {
    type: String,
    required: true,
    unique: true
  },
  playerId: {
    type: String,
    required: true
  },
  playerName: {
    type: String,
    required: true
  },
  totalHours: {
    type: Number,
    required: true,
    default: 0
  },
  orgServers: [ServerInfoSchema],
  kills: {
    type: Number,
    required: true,
    default: 0
  },
  deaths: {
    type: Number,
    required: true,
    default: 0
  },
  killDeathRatio: {
    type: Number,
    required: true,
    default: 0
  },
  rocketsShot: {
    type: Number,
    required: true,
    default: 0
  },
  c4Thrown: {
    type: Number,
    required: true,
    default: 0
  },
  headshots: {
    type: Number,
    required: true,
    default: 0
  },
  resourcesGathered: {
    type: Map,
    of: Number,
    default: {}
  },
  structuresBuilt: {
    type: Number,
    required: true,
    default: 0
  },
  itemsCrafted: {
    type: Number,
    required: true,
    default: 0
  },
  explosivesUsed: {
    type: Number,
    required: true,
    default: 0
  },
  animalsKilled: {
    type: Number,
    required: true,
    default: 0
  },
  barrelsLooted: {
    type: Number,
    required: true,
    default: 0
  },
  recentActivity: [ActivityLogEntrySchema],
  dailyStats: [DailyStatsSchema],
  lastUpdated: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Pre-save middleware to clean up old data and calculate totals
PlayerStatsSchema.pre('save', function(next) {
  // Clean up daily stats older than 14 days
  const fourteenDaysAgo = new Date();
  fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);
  const cutoffDate = fourteenDaysAgo.toISOString().split('T')[0];

  // Filter out old daily stats
  this.dailyStats = this.dailyStats.filter(dayStats => dayStats.date >= cutoffDate);

  // Calculate totals from daily stats
  this.kills = this.dailyStats.reduce((total, day) => total + day.kills, 0);
  this.deaths = this.dailyStats.reduce((total, day) => total + day.deaths, 0);
  this.rocketsShot = this.dailyStats.reduce((total, day) => total + day.rocketsShot, 0);
  this.c4Thrown = this.dailyStats.reduce((total, day) => total + day.c4Thrown, 0);
  this.headshots = this.dailyStats.reduce((total, day) => total + day.headshots, 0);
  this.resourcesGathered = this.dailyStats.reduce((acc, day) => {
    for (const [type, amount] of Object.entries(day.resourcesGathered)) {
      acc[type] = (acc[type] || 0) + amount;
    }
    return acc;
  }, {});
  this.structuresBuilt = this.dailyStats.reduce((total, day) => total + day.structuresBuilt, 0);
  this.itemsCrafted = this.dailyStats.reduce((total, day) => total + day.itemsCrafted, 0);
  this.explosivesUsed = this.dailyStats.reduce((total, day) => total + day.explosivesUsed, 0);
  this.animalsKilled = this.dailyStats.reduce((total, day) => total + day.animalsKilled, 0);
  this.barrelsLooted = this.dailyStats.reduce((total, day) => total + day.barrelsLooted, 0);

  // Calculate K/D ratio
  if (this.deaths > 0) {
    this.killDeathRatio = Number((this.kills / this.deaths).toFixed(2));
  } else {
    this.killDeathRatio = this.kills;
  }

  // Update recent activity from daily stats
  this.recentActivity = this.dailyStats
    .flatMap(day => day.activities)
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 50);

  this.lastUpdated = new Date();
  next();
});

// Index for efficient queries
PlayerStatsSchema.index({ steamId: 1 });
PlayerStatsSchema.index({ playerName: 1 });
PlayerStatsSchema.index({ lastUpdated: -1 });

// Export the model, handling the case where it might already be compiled
export const PlayerStats = mongoose.models.PlayerStats || mongoose.model<IPlayerStats>('PlayerStats', PlayerStatsSchema);