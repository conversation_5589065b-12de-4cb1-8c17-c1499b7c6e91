{"name": "playerstats", "module": "index.ts", "type": "module", "private": true, "scripts": {"start": "bun run index.ts", "dev": "bun --watch index.ts", "api": "bun run api/index.ts", "api:dev": "bun --watch api/index.ts", "api:start": "bun run api/index.ts"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@types/mongoose": "^5.11.97", "discord.js": "^14.19.3", "elysia": "^1.3.1", "mongoose": "^8.15.0"}}