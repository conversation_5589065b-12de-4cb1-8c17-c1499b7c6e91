# Plugin Integration Guide

This guide shows how to integrate your Rust plugin with the Player Stats API to send and retrieve player data.

## API Endpoints for Plugins

The API runs on `http://localhost:3000` when the Discord bot is started.

### 1. Send Player Data from Plugin

**POST** `/api/player`
```json
{
  "steamId": "76561199182780054",
  "forceRefresh": true
}
```

This endpoint will:
- Fetch fresh data from Battlemetrics and Steam APIs
- Calculate K/D ratio automatically
- Store/update the data in MongoDB
- Return the complete player stats

### 2. Get Player Data for Plugin

**GET** `/api/player/{steamId}`

Returns stored player data including:
- Total playtime hours
- Organization server hours
- Kills, deaths, and K/D ratio
- Recent activity
- Last updated timestamp

### 3. Update Player Stats from Plugin

**PUT** `/api/player/{steamId}`
```json
{
  "kills": 50,
  "deaths": 25,
  "totalHours": 300.5
}
```

Use this to update specific stats from your plugin's tracking.

## Example Plugin Integration (C#)

### Basic HTTP Client Setup
```csharp
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

public class PlayerStatsAPI
{
    private readonly HttpClient _httpClient;
    private readonly string _baseUrl;

    public PlayerStatsAPI(string baseUrl = "http://localhost:3000")
    {
        _httpClient = new HttpClient();
        _baseUrl = baseUrl;
    }

    public async Task<PlayerStats> GetPlayerStatsAsync(string steamId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/player/{steamId}");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ApiResponse<PlayerStats>>(json);
                return result.Data;
            }
            
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error fetching player stats: {ex.Message}");
            return null;
        }
    }

    public async Task<bool> UpdatePlayerStatsAsync(string steamId, bool forceRefresh = false)
    {
        try
        {
            var data = new { steamId, forceRefresh };
            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync($"{_baseUrl}/api/player", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating player stats: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> UpdateKillsDeathsAsync(string steamId, int kills, int deaths)
    {
        try
        {
            var data = new { kills, deaths };
            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"{_baseUrl}/api/player/{steamId}", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating kills/deaths: {ex.Message}");
            return false;
        }
    }
}

// Data models
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public T Data { get; set; }
    public string Message { get; set; }
}

public class PlayerStats
{
    public string SteamId { get; set; }
    public string PlayerId { get; set; }
    public string PlayerName { get; set; }
    public double TotalHours { get; set; }
    public int Kills { get; set; }
    public int Deaths { get; set; }
    public double KillDeathRatio { get; set; }
    public DateTime LastUpdated { get; set; }
}
```

### Plugin Usage Example
```csharp
public class MyRustPlugin : RustPlugin
{
    private PlayerStatsAPI _statsAPI;

    void Init()
    {
        _statsAPI = new PlayerStatsAPI();
    }

    void OnPlayerConnected(BasePlayer player)
    {
        // Update player stats when they connect
        var steamId = player.UserIDString;
        _statsAPI.UpdatePlayerStatsAsync(steamId, forceRefresh: false);
    }

    void OnPlayerDeath(BasePlayer victim, HitInfo info)
    {
        if (info?.InitiatorPlayer != null)
        {
            // Update killer's stats
            var killerSteamId = info.InitiatorPlayer.UserIDString;
            var killerStats = await _statsAPI.GetPlayerStatsAsync(killerSteamId);
            
            if (killerStats != null)
            {
                await _statsAPI.UpdateKillsDeathsAsync(
                    killerSteamId, 
                    killerStats.Kills + 1, 
                    killerStats.Deaths
                );
            }
        }

        // Update victim's stats
        var victimSteamId = victim.UserIDString;
        var victimStats = await _statsAPI.GetPlayerStatsAsync(victimSteamId);
        
        if (victimStats != null)
        {
            await _statsAPI.UpdateKillsDeathsAsync(
                victimSteamId, 
                victimStats.Kills, 
                victimStats.Deaths + 1
            );
        }
    }

    [ChatCommand("stats")]
    void StatsCommand(BasePlayer player, string command, string[] args)
    {
        var steamId = player.UserIDString;
        
        Task.Run(async () =>
        {
            var stats = await _statsAPI.GetPlayerStatsAsync(steamId);
            
            if (stats != null)
            {
                player.ChatMessage($"Your Stats:\n" +
                    $"Total Hours: {stats.TotalHours:F1}\n" +
                    $"Kills: {stats.Kills}\n" +
                    $"Deaths: {stats.Deaths}\n" +
                    $"K/D Ratio: {stats.KillDeathRatio:F2}");
            }
            else
            {
                player.ChatMessage("Could not retrieve your stats. Please try again later.");
            }
        });
    }
}
```

## JavaScript/Node.js Example

```javascript
const axios = require('axios');

class PlayerStatsAPI {
    constructor(baseUrl = 'http://localhost:3000') {
        this.baseUrl = baseUrl;
    }

    async getPlayerStats(steamId) {
        try {
            const response = await axios.get(`${this.baseUrl}/api/player/${steamId}`);
            return response.data.data;
        } catch (error) {
            console.error('Error fetching player stats:', error.message);
            return null;
        }
    }

    async updatePlayerStats(steamId, forceRefresh = false) {
        try {
            const response = await axios.post(`${this.baseUrl}/api/player`, {
                steamId,
                forceRefresh
            });
            return response.data.success;
        } catch (error) {
            console.error('Error updating player stats:', error.message);
            return false;
        }
    }

    async updateKillsDeaths(steamId, kills, deaths) {
        try {
            const response = await axios.put(`${this.baseUrl}/api/player/${steamId}`, {
                kills,
                deaths
            });
            return response.data.success;
        } catch (error) {
            console.error('Error updating kills/deaths:', error.message);
            return false;
        }
    }
}

// Usage
const api = new PlayerStatsAPI();

// Get player stats
const stats = await api.getPlayerStats('76561199182780054');
console.log('Player stats:', stats);

// Update player data
await api.updatePlayerStats('76561199182780054', true);

// Update kills/deaths
await api.updateKillsDeaths('76561199182780054', 50, 25);
```

## Testing the API

You can test the API endpoints using curl:

```bash
# Get player stats
curl http://localhost:3000/api/player/76561199182780054

# Create/update player stats
curl -X POST http://localhost:3000/api/player \
  -H "Content-Type: application/json" \
  -d '{"steamId":"76561199182780054","forceRefresh":true}'

# Update kills/deaths
curl -X PUT http://localhost:3000/api/player/76561199182780054 \
  -H "Content-Type: application/json" \
  -d '{"kills":50,"deaths":25}'

# Search players
curl "http://localhost:3000/api/search?name=Bob"
```

## Error Handling

The API returns consistent error responses:
```json
{
  "success": false,
  "error": "Error description",
  "details": "Additional details if available"
}
```

Always check the `success` field before processing the data.

## Rate Limiting

The API respects Battlemetrics rate limits (1 second between requests). For high-frequency updates, consider:

1. Caching player data locally
2. Batching updates
3. Using the `forceRefresh: false` option to use cached data

## Data Flow

1. **Plugin** → API: Send player events (kills, deaths, connections)
2. **API** → Battlemetrics/Steam: Fetch fresh player data
3. **API** → MongoDB: Store/update player stats
4. **Discord Bot** → API: Retrieve data for Discord commands
5. **Plugin** → API: Get player stats for in-game commands
