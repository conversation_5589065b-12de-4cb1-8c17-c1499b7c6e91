import mongoose from 'mongoose';
import { PlayerStats, IPlayerStats, IActivityLogEntry, IDailyStats } from '../models/PlayerStats.js';

interface DatabaseConfig {
  mongoUrl: string;
  dbName?: string;
}

export class DatabaseService {
  private static instance: DatabaseService;
  private isConnected: boolean = false;

  private constructor() {}

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  async connect(config: DatabaseConfig): Promise<void> {
    if (this.isConnected) {
      console.log('Database already connected');
      return;
    }

    try {
      const connectionOptions = {
        dbName: config.dbName || 'rust_player_stats',
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferCommands: false
      };

      await mongoose.connect(config.mongoUrl, connectionOptions);

      this.isConnected = true;
      console.log(`Connected to MongoDB database: ${connectionOptions.dbName}`);

      // Handle connection events
      mongoose.connection.on('error', (error) => {
        console.error('MongoDB connection error:', error);
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        console.log('MongoDB disconnected');
        this.isConnected = false;
      });

      mongoose.connection.on('reconnected', () => {
        console.log('MongoDB reconnected');
        this.isConnected = true;
      });

    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await mongoose.disconnect();
      this.isConnected = false;
      console.log('Disconnected from MongoDB');
    } catch (error) {
      console.error('Error disconnecting from MongoDB:', error);
      throw error;
    }
  }

  isConnectionActive(): boolean {
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  getConnection() {
    return mongoose.connection;
  }
}

// Daily Stats Service Logic
export class DailyStatsService {
  async recordActivity(steamId: string, activity: IActivityLogEntry): Promise<IPlayerStats> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const validActivityTypes = [
      'rocket_shot', 'c4_thrown', 'satchel_thrown', 'explosive_thrown',
      'resource_gathered', 'structure_built', 'item_crafted',
      'animal_killed', 'barrel_looted'
    ];

    if (!validActivityTypes.includes(activity.type)) {
      throw new Error(`Invalid activity type: ${activity.type}`);
    }

    const date = new Date(activity.timestamp).toISOString().split('T')[0];
    const update: any = {
      $set: { lastUpdated: new Date() },
      $push: { recentActivity: activity }
    };

    update.$setOnInsert = {
      steamId,
      playerId: steamId,
      playerName: 'Unknown',
      totalHours: 0,
      kills: 0,
      deaths: 0,
      killDeathRatio: 0,
      rocketsShot: 0,
      c4Thrown: 0,
      headshots: 0,
      resourcesGathered: {},
      structuresBuilt: 0,
      itemsCrafted: 0,
      explosivesUsed: 0,
      animalsKilled: 0,
      barrelsLooted: 0,
      orgServers: [],
      dailyStats: []
    };

    const incUpdate: any = {};

    switch (activity.type) {
      case 'rocket_shot':
        incUpdate['dailyStats.$.rocketsShot'] = 1;
        break;
      case 'c4_thrown':
        incUpdate['dailyStats.$.c4Thrown'] = 1;
        incUpdate['dailyStats.$.explosivesUsed'] = 1;
        break;
      case 'satchel_thrown':
      case 'explosive_thrown':
        incUpdate['dailyStats.$.explosivesUsed'] = 1;
        break;
      case 'resource_gathered':
        if (activity.data.resourceType && activity.data.amount) {
          incUpdate[`dailyStats.$.resourcesGathered.${activity.data.resourceType}`] = activity.data.amount;
        }
        break;
      case 'structure_built':
        incUpdate['dailyStats.$.structuresBuilt'] = 1;
        break;
      case 'item_crafted':
        incUpdate['dailyStats.$.itemsCrafted'] = 1;
        break;
      case 'animal_killed':
        incUpdate['dailyStats.$.animalsKilled'] = 1;
        break;
      case 'barrel_looted':
        incUpdate['dailyStats.$.barrelsLooted'] = 1;
        break;
    }

    let player = await PlayerStats.findOneAndUpdate(
      { steamId, 'dailyStats.date': date },
      {
        $push: { 'dailyStats.$.activities': activity },
        $inc: incUpdate
      },
      { new: true }
    );

    if (!player) {
      const newDailyStats: IDailyStats = {
        date,
        kills: 0,
        deaths: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: 0,
        serverSessions: [],
        activities: [activity]
      };

      switch (activity.type) {
        case 'rocket_shot':
          newDailyStats.rocketsShot = 1;
          break;
        case 'c4_thrown':
          newDailyStats.c4Thrown = 1;
          newDailyStats.explosivesUsed = 1;
          break;
        case 'satchel_thrown':
        case 'explosive_thrown':
          newDailyStats.explosivesUsed = 1;
          break;
        case 'resource_gathered':
          if (activity.data.resourceType && activity.data.amount) {
            newDailyStats.resourcesGathered[activity.data.resourceType] = activity.data.amount;
          }
          break;
        case 'structure_built':
          newDailyStats.structuresBuilt = 1;
          break;
        case 'item_crafted':
          newDailyStats.itemsCrafted = 1;
          break;
        case 'animal_killed':
          newDailyStats.animalsKilled = 1;
          break;
        case 'barrel_looted':
          newDailyStats.barrelsLooted = 1;
          break;
      }

      player = await PlayerStats.findOneAndUpdate(
        { steamId },
        {
          $set: { lastUpdated: new Date() },
          $push: { recentActivity: activity, dailyStats: newDailyStats },
          $setOnInsert: update.$setOnInsert
        },
        { upsert: true, new: true }
      );
    }

    return player!;
  }

  async recordKill(steamId: string, activity?: IActivityLogEntry): Promise<IPlayerStats> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const date = activity ? new Date(activity.timestamp).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
    const update: any = {
      $set: { lastUpdated: new Date() }
    };

    if (activity) {
      update.$push = { recentActivity: activity };
    }

    update.$setOnInsert = {
      steamId,
      playerId: steamId,
      playerName: 'Unknown',
      totalHours: 0,
      kills: 0,
      deaths: 0,
      killDeathRatio: 0,
      rocketsShot: 0,
      c4Thrown: 0,
      headshots: 0,
      resourcesGathered: {},
      structuresBuilt: 0,
      itemsCrafted: 0,
      explosivesUsed: 0,
      animalsKilled: 0,
      barrelsLooted: 0,
      orgServers: [],
      dailyStats: []
    };

    let player = await PlayerStats.findOneAndUpdate(
      { steamId, 'dailyStats.date': date },
      {
        $inc: {
          'dailyStats.$.kills': 1,
          'dailyStats.$.headshots': activity?.type === 'headshot' ? 1 : 0
        },
        ...(activity && { $push: { 'dailyStats.$.activities': activity } })
      },
      { new: true }
    );

    if (!player) {
      const newDailyStats: IDailyStats = {
        date,
        kills: 1,
        deaths: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: activity?.type === 'headshot' ? 1 : 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: 0,
        serverSessions: [],
        activities: activity ? [activity] : []
      };

      player = await PlayerStats.findOneAndUpdate(
        { steamId },
        {
          $set: { lastUpdated: new Date() },
          $push: {
            ...(activity && { recentActivity: activity }),
            dailyStats: newDailyStats
          },
          $setOnInsert: update.$setOnInsert
        },
        { upsert: true, new: true }
      );
    }

    return player!;
  }

  async recordDeath(steamId: string, activity?: IActivityLogEntry): Promise<IPlayerStats> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const date = activity ? new Date(activity.timestamp).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
    const update: any = {
      $set: { lastUpdated: new Date() }
    };

    if (activity) {
      update.$push = { recentActivity: activity };
    }

    update.$setOnInsert = {
      steamId,
      playerId: steamId,
      playerName: 'Unknown',
      totalHours: 0,
      kills: 0,
      deaths: 0,
      killDeathRatio: 0,
      rocketsShot: 0,
      c4Thrown: 0,
      headshots: 0,
      resourcesGathered: {},
      structuresBuilt: 0,
      itemsCrafted: 0,
      explosivesUsed: 0,
      animalsKilled: 0,
      barrelsLooted: 0,
      orgServers: [],
      dailyStats: []
    };

    let player = await PlayerStats.findOneAndUpdate(
      { steamId, 'dailyStats.date': date },
      {
        $inc: { 'dailyStats.$.deaths': 1 },
        ...(activity && { $push: { 'dailyStats.$.activities': activity } })
      },
      { new: true }
    );

    if (!player) {
      const newDailyStats: IDailyStats = {
        date,
        kills: 0,
        deaths: 1,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: 0,
        serverSessions: [],
        activities: activity ? [activity] : []
      };

      player = await PlayerStats.findOneAndUpdate(
        { steamId },
        {
          $set: { lastUpdated: new Date() },
          $push: {
            ...(activity && { recentActivity: activity }),
            dailyStats: newDailyStats
          },
          $setOnInsert: update.$setOnInsert
        },
        { upsert: true, new: true }
      );
    }

    return player!;
  }

  async recordPlaytime(steamId: string, serverId: string, serverName: string, minutes: number): Promise<IPlayerStats> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const date = new Date().toISOString().split('T')[0];
    const update: any = {
      $set: { lastUpdated: new Date() },
      $setOnInsert: {
        steamId,
        playerId: steamId,
        playerName: 'Unknown',
        totalHours: 0,
        kills: 0,
        deaths: 0,
        killDeathRatio: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        orgServers: [],
        dailyStats: []
      }
    };

    let player = await PlayerStats.findOneAndUpdate(
      { steamId, 'dailyStats.date': date },
      {
        $inc: { 'dailyStats.$.playtimeMinutes': minutes },
        $push: {
          'dailyStats.$.serverSessions': { serverId, serverName, minutes }
        }
      },
      { new: true }
    );

    if (!player) {
      const newDailyStats: IDailyStats = {
        date,
        kills: 0,
        deaths: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: minutes,
        serverSessions: [{ serverId, serverName, minutes }],
        activities: []
      };

      player = await PlayerStats.findOneAndUpdate(
        { steamId },
        {
          $set: { lastUpdated: new Date() },
          $push: { dailyStats: newDailyStats },
          $setOnInsert: update.$setOnInsert
        },
        { upsert: true, new: true }
      );
    }

    return player!;
  }

  async getDailyStats(steamId: string, startDate?: string, endDate?: string): Promise<IDailyStats[]> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const query: any = { steamId };
    if (startDate || endDate) {
      query['dailyStats.date'] = {};
      if (startDate) query['dailyStats.date'].$gte = startDate;
      if (endDate) query['dailyStats.date'].$lte = endDate;
    }

    const player = await PlayerStats.findOne(query, { dailyStats: 1 });
    return player ? player.dailyStats : [];
  }

  async getAggregatedStats(steamId: string, days: number): Promise<Partial<IDailyStats>> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    const cutoffDateStr = cutoffDate.toISOString().split('T')[0];

    const player = await PlayerStats.findOne(
      { steamId, 'dailyStats.date': { $gte: cutoffDateStr } },
      { dailyStats: 1 }
    );

    if (!player) {
      return {
        kills: 0,
        deaths: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: 0,
        serverSessions: []
      };
    }

    return player.dailyStats.reduce(
      (acc: any, day: any) => ({
        kills: acc.kills + day.kills,
        deaths: acc.deaths + day.deaths,
        rocketsShot: acc.rocketsShot + day.rocketsShot,
        c4Thrown: acc.c4Thrown + day.c4Thrown,
        headshots: acc.headshots + day.headshots,
        resourcesGathered: Object.entries(day.resourcesGathered).reduce((res, [type, amount]) => {
          res[type] = (res[type] || 0) + amount;
          return res;
        }, acc.resourcesGathered),
        structuresBuilt: acc.structuresBuilt + day.structuresBuilt,
        itemsCrafted: acc.itemsCrafted + day.itemsCrafted,
        explosivesUsed: acc.explosivesUsed + day.explosivesUsed,
        animalsKilled: acc.animalsKilled + day.animalsKilled,
        barrelsLooted: acc.barrelsLooted + day.barrelsLooted,
        playtimeMinutes: acc.playtimeMinutes + day.playtimeMinutes,
        serverSessions: [...acc.serverSessions, ...day.serverSessions]
      }),
      {
        kills: 0,
        deaths: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: 0,
        serverSessions: []
      }
    );
  }
}
