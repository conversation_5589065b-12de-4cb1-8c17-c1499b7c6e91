interface ServerInfo {
  id: string;
  name: string;
  ip: string;
  port: number;
  players: number;
  maxPlayers: number;
  status: string;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

export class ServerInfoService {
  private apiUrl: string;
  private cache: Map<string, CacheEntry<ServerInfo>>;
  private cacheTimeMs: number;

  constructor(apiUrl: string, cacheTimeMinutes: number = 30) {
    this.apiUrl = apiUrl;
    this.cache = new Map();
    this.cacheTimeMs = cacheTimeMinutes * 60 * 1000;
  }

  private isCacheValid(serverId: string): boolean {
    const cached = this.cache.get(serverId);
    if (!cached) return false;
    
    const cacheAge = Date.now() - cached.timestamp;
    return cacheAge < this.cacheTimeMs;
  }

  private async makeRequest(url: string): Promise<any> {
    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'PlayerStatsBot/1.0'
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  async getServerInfo(serverId: string): Promise<ServerInfo | null> {
    // Check cache first
    if (this.isCacheValid(serverId)) {
      return this.cache.get(serverId)!.data;
    }

    try {
      const url = `${this.apiUrl}/servers/${serverId}`;
      const response = await this.makeRequest(url);
      
      if (!response.data) {
        return null;
      }

      const server = response.data;
      const serverInfo: ServerInfo = {
        id: server.id,
        name: server.attributes.name,
        ip: server.attributes.ip,
        port: server.attributes.port,
        players: server.attributes.players,
        maxPlayers: server.attributes.maxPlayers,
        status: server.attributes.status
      };

      // Cache the result
      this.cache.set(serverId, {
        data: serverInfo,
        timestamp: Date.now()
      });

      return serverInfo;
    } catch (error) {
      console.error(`Error fetching server info for ${serverId}:`, error);
      return null;
    }
  }

  async getMultipleServerInfo(serverIds: string[]): Promise<{ [serverId: string]: ServerInfo }> {
    const serverInfoMap: { [serverId: string]: ServerInfo } = {};
    
    // Fetch all server info in parallel
    const promises = serverIds.map(async (serverId) => {
      const info = await this.getServerInfo(serverId);
      if (info) {
        serverInfoMap[serverId] = info;
      }
    });

    await Promise.all(promises);
    return serverInfoMap;
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheSize(): number {
    return this.cache.size;
  }
}
