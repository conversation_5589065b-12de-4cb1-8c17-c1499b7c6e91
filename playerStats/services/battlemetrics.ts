interface BattlemetricsConfig {
  serverIds: string[];
  apiUrl: string;
}

interface SettingsConfig {
  killDeathDays: number;
  cacheTimeMinutes: number;
  rateLimitMs: number;
}

interface PlayerSession {
  id: string;
  type: string;
  attributes: {
    start: string;
    stop: string | null;
  };
  relationships: {
    server: {
      data: {
        id: string;
      };
    };
  };
}

interface ActivityLogEntry {
  id: string;
  type: string;
  attributes: {
    timestamp: string;
    data: {
      killer?: {
        name: string;
        id: string;
      };
      victim?: {
        name: string;
        id: string;
      };
      weapon?: string;
      distance?: number;
    };
  };
}

interface PlayerStats {
  playerId: string;
  playerName: string;
  totalHours: number;
  serverHours: { [serverId: string]: number };
  kills: number;
  deaths: number;
  killDeathRatio: number;
  recentActivity: ActivityLogEntry[];
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

export class BattlemetricsAPI {
  private config: BattlemetricsConfig;
  private settings: SettingsConfig;
  private cache: Map<string, CacheEntry<any>>;
  private lastRequestTime: number = 0;

  constructor(config: BattlemetricsConfig, settings: SettingsConfig) {
    this.config = config;
    this.settings = settings;
    this.cache = new Map();
  }

  private async rateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.settings.rateLimitMs) {
      const waitTime = this.settings.rateLimitMs - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
  }

  private isCacheValid<T>(cacheKey: string): boolean {
    const cached = this.cache.get(cacheKey);
    if (!cached) return false;
    
    const cacheAge = Date.now() - cached.timestamp;
    const maxAge = this.settings.cacheTimeMinutes * 60 * 1000;
    
    return cacheAge < maxAge;
  }

  private getCached<T>(cacheKey: string): T | null {
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey)!.data;
    }
    return null;
  }

  private setCache<T>(cacheKey: string, data: T): void {
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  private async makeRequest(url: string): Promise<any> {
    await this.rateLimit();
    
    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'PlayerStatsBot/1.0'
      }
    });

    if (!response.ok) {
      throw new Error(`Battlemetrics API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  async findPlayerByName(playerName: string): Promise<any> {
    const cacheKey = `player_search_${playerName.toLowerCase()}`;
    const cached = this.getCached<any>(cacheKey);
    if (cached) return cached;

    const url = `${this.config.apiUrl}/players?filter[search]=${encodeURIComponent(playerName)}&page[size]=10`;
    const response = await this.makeRequest(url);
    
    this.setCache(cacheKey, response);
    return response;
  }

  async getPlayerSessions(playerId: string): Promise<PlayerSession[]> {
    const cacheKey = `sessions_${playerId}`;
    const cached = this.getCached<PlayerSession[]>(cacheKey);
    if (cached) return cached;

    const sessions: PlayerSession[] = [];
    let nextUrl = `${this.config.apiUrl}/players/${playerId}/relationships/sessions?include=server&page[size]=100`;

    while (nextUrl) {
      const response = await this.makeRequest(nextUrl);
      sessions.push(...response.data);
      
      nextUrl = response.links?.next || null;
      
      // Limit to prevent infinite loops
      if (sessions.length > 1000) break;
    }

    this.setCache(cacheKey, sessions);
    return sessions;
  }

  async getPlayerActivity(playerId: string): Promise<ActivityLogEntry[]> {
    const cacheKey = `activity_${playerId}`;
    const cached = this.getCached<ActivityLogEntry[]>(cacheKey);
    if (cached) return cached;

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.settings.killDeathDays);
    const cutoffTimestamp = cutoffDate.toISOString();

    const activities: ActivityLogEntry[] = [];
    let nextUrl = `${this.config.apiUrl}/players/${playerId}/relationships/activity?filter[timestamp]=${cutoffTimestamp}&page[size]=100`;

    while (nextUrl) {
      const response = await this.makeRequest(nextUrl);
      const filteredActivities = response.data.filter((activity: ActivityLogEntry) => {
        return activity.type === 'rustKill' || activity.type === 'rustDeath';
      });
      
      activities.push(...filteredActivities);
      
      nextUrl = response.links?.next || null;
      
      // Limit to prevent infinite loops
      if (activities.length > 500) break;
    }

    this.setCache(cacheKey, activities);
    return activities;
  }

  calculateServerHours(sessions: PlayerSession[]): { [serverId: string]: number } {
    const serverHours: { [serverId: string]: number } = {};
    
    // Initialize all configured servers with 0 hours
    this.config.serverIds.forEach(serverId => {
      serverHours[serverId] = 0;
    });

    sessions.forEach(session => {
      const serverId = session.relationships.server.data.id;
      
      // Only count hours for configured servers
      if (!this.config.serverIds.includes(serverId)) return;

      const startTime = new Date(session.attributes.start);
      const endTime = session.attributes.stop ? new Date(session.attributes.stop) : new Date();
      
      const durationMs = endTime.getTime() - startTime.getTime();
      const durationHours = durationMs / (1000 * 60 * 60);
      
      serverHours[serverId] = (serverHours[serverId] || 0) + durationHours;
    });

    return serverHours;
  }

  calculateKillsAndDeaths(activities: ActivityLogEntry[]): { kills: number; deaths: number } {
    let kills = 0;
    let deaths = 0;

    activities.forEach(activity => {
      if (activity.type === 'rustKill') {
        kills++;
      } else if (activity.type === 'rustDeath') {
        deaths++;
      }
    });

    return { kills, deaths };
  }

  async getPlayerStats(playerName: string): Promise<PlayerStats | null> {
    try {
      // Find player by name
      const searchResult = await this.findPlayerByName(playerName);
      
      if (!searchResult.data || searchResult.data.length === 0) {
        return null;
      }

      const player = searchResult.data[0];
      const playerId = player.id;

      // Get sessions and activity in parallel
      const [sessions, activities] = await Promise.all([
        this.getPlayerSessions(playerId),
        this.getPlayerActivity(playerId)
      ]);

      // Calculate stats
      const serverHours = this.calculateServerHours(sessions);
      const totalHours = Object.values(serverHours).reduce((sum, hours) => sum + hours, 0);
      const { kills, deaths } = this.calculateKillsAndDeaths(activities);
      const killDeathRatio = deaths > 0 ? kills / deaths : kills;

      return {
        playerId,
        playerName: player.attributes.name,
        totalHours,
        serverHours,
        kills,
        deaths,
        killDeathRatio,
        recentActivity: activities.slice(0, 10) // Last 10 activities
      };

    } catch (error) {
      console.error('Error fetching player stats:', error);
      throw error;
    }
  }
}
