interface BattlemetricsConfig {
  orgId: string;
  apiUrl: string;
  apiToken?: string;
  serverIds?: string[];
}

interface SettingsConfig {
  killDeathDays: number;
  cacheTimeMinutes: number;
  rateLimitMs: number;
}

interface PlayerSession {
  id: string;
  type: string;
  attributes: {
    start: string;
    stop: string | null;
  };
  relationships: {
    server: {
      data: {
        id: string;
      };
    };
  };
}

interface ActivityLogEntry {
  id: string;
  type: string;
  attributes: {
    timestamp: string;
    data: {
      killer?: {
        name: string;
        id: string;
      };
      victim?: {
        name: string;
        id: string;
      };
      weapon?: string;
      distance?: number;
    };
  };
}

interface ServerInfo {
  id: string;
  name: string;
  hours: number;
}

interface PlayerStats {
  playerId: string;
  playerName: string;
  totalHours: number;
  orgServers: ServerInfo[];
  kills: number;
  deaths: number;
  killDeathRatio: number;
  recentActivity: ActivityLogEntry[];
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

export class BattlemetricsAPI {
  private config: BattlemetricsConfig;
  private settings: SettingsConfig;
  private cache: Map<string, CacheEntry<any>>;
  private lastRequestTime: number = 0;

  constructor(config: BattlemetricsConfig, settings: SettingsConfig) {
    this.config = config;
    this.settings = settings;
    this.cache = new Map();
  }

  private async rateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.settings.rateLimitMs) {
      const waitTime = this.settings.rateLimitMs - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  private isCacheValid<T>(cacheKey: string): boolean {
    const cached = this.cache.get(cacheKey);
    if (!cached) return false;

    const cacheAge = Date.now() - cached.timestamp;
    const maxAge = this.settings.cacheTimeMinutes * 60 * 1000;

    return cacheAge < maxAge;
  }

  private getCached<T>(cacheKey: string): T | null {
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey)!.data;
    }
    return null;
  }

  private setCache<T>(cacheKey: string, data: T): void {
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  private async makeRequest(url: string): Promise<any> {
    await this.rateLimit();

    const headers: Record<string, string> = {
      'Accept': 'application/json',
      'User-Agent': 'PlayerStatsBot/1.0'
    };

    // Add authorization header if API token is provided
    if (this.config.apiToken) {
      headers['Authorization'] = `Bearer ${this.config.apiToken}`;
    }

    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Battlemetrics API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  async findPlayerByName(playerName: string): Promise<any> {
    const cacheKey = `player_search_${playerName.toLowerCase()}`;
    const cached = this.getCached<any>(cacheKey);
    if (cached) return cached;

    const url = `${this.config.apiUrl}/players?filter[search]=${encodeURIComponent(playerName)}&page[size]=10`;
    const response = await this.makeRequest(url);

    this.setCache(cacheKey, response);
    return response;
  }

  async findPlayerBySteamId(steamId: string): Promise<any> {
    const cacheKey = `player_steam_${steamId}`;
    const cached = this.getCached<any>(cacheKey);
    if (cached) return cached;

    try {
      // Method 1: Search by Steam ID using the search filter with steam: prefix
      let url = `${this.config.apiUrl}/players?filter[search]=steam:${steamId}&page[size]=10`;
      console.log(`Trying Steam ID search method 1: ${url}`);
      let response = await this.makeRequest(url);

      if (response.data && response.data.length > 0) {
        console.log(`Method 1 success: Found ${response.data.length} players`);
        console.log(`First player: ${response.data[0].attributes.name} (ID: ${response.data[0].id})`);
        this.setCache(cacheKey, response);
        return response;
      }

      // Method 2: Search by Steam ID using identifiers filter
      url = `${this.config.apiUrl}/players?filter[identifiers]=${steamId}&page[size]=10`;
      console.log(`Trying Steam ID search method 2: ${url}`);
      response = await this.makeRequest(url);

      if (response.data && response.data.length > 0) {
        console.log(`Method 2 success: Found ${response.data.length} players`);
        console.log(`First player: ${response.data[0].attributes.name} (ID: ${response.data[0].id})`);
        this.setCache(cacheKey, response);
        return response;
      }

      // Method 3: Search by Steam ID directly in search
      url = `${this.config.apiUrl}/players?filter[search]=${steamId}&page[size]=10`;
      console.log(`Trying Steam ID search method 3: ${url}`);
      response = await this.makeRequest(url);

      if (response.data && response.data.length > 0) {
        console.log(`Method 3 success: Found ${response.data.length} players`);
        console.log(`First player: ${response.data[0].attributes.name} (ID: ${response.data[0].id})`);
      } else {
        console.log(`Method 3 failed: No players found for Steam ID ${steamId}`);
      }

      this.setCache(cacheKey, response);
      return response;

    } catch (error) {
      console.error('Error searching by Steam ID:', error);
      // Return empty result
      return { data: [] };
    }
  }

  async getPlayerSessions(playerId: string): Promise<PlayerSession[]> {
    const cacheKey = `sessions_${playerId}`;
    const cached = this.getCached<PlayerSession[]>(cacheKey);
    if (cached) return cached;

    const sessions: PlayerSession[] = [];
    let nextUrl = `${this.config.apiUrl}/players/${playerId}/relationships/sessions?include=server&page[size]=100`;

    while (nextUrl) {
      const response = await this.makeRequest(nextUrl);
      if (response.data) {
        sessions.push(...response.data);
      }

      nextUrl = response.links?.next || null;

      // Limit to prevent infinite loops
      if (sessions.length > 1000) break;
    }

    this.setCache(cacheKey, sessions);
    return sessions;
  }

  async getPlayerActivity(playerId: string): Promise<ActivityLogEntry[]> {
    const cacheKey = `activity_${playerId}`;
    const cached = this.getCached<ActivityLogEntry[]>(cacheKey);
    if (cached) return cached;

    // Note: The Battlemetrics API documentation doesn't show a direct activity endpoint
    // This might need to be implemented differently or may require special permissions
    // For now, we'll return an empty array and log that this feature needs implementation
    console.log(`Activity tracking for player ${playerId} not yet implemented - Battlemetrics API endpoint unclear`);

    const activities: ActivityLogEntry[] = [];
    this.setCache(cacheKey, activities);
    return activities;
  }

  async getOrganizationServers(): Promise<any[]> {
    const cacheKey = `org_servers_${this.config.orgId}`;
    const cached = this.getCached<any[]>(cacheKey);
    if (cached) return cached;

    // Skip organization API call and go directly to manual server IDs
    if (this.config.serverIds && this.config.serverIds.length > 0) {
      console.log(`Using manual server IDs: ${this.config.serverIds.join(', ')}`);

      try {
        const serverPromises = this.config.serverIds.map(async (serverId) => {
          try {
            const serverUrl = `${this.config.apiUrl}/servers/${serverId}`;
            const serverResponse = await this.makeRequest(serverUrl);
            return serverResponse.data;
          } catch (serverError) {
            console.error(`Error fetching server ${serverId}:`, serverError);
            return null;
          }
        });

        const servers = (await Promise.all(serverPromises)).filter(server => server !== null);
        console.log(`Successfully fetched ${servers.length} servers from manual IDs`);

        this.setCache(cacheKey, servers);
        return servers;
      } catch (fallbackError) {
        console.error('Manual server ID fallback failed:', fallbackError);
      }
    }

    console.log('No manual server IDs configured, returning empty array');
    return [];
  }

  calculateTotalHours(sessions: PlayerSession[]): number {
    let totalHours = 0;

    sessions.forEach(session => {
      const startTime = new Date(session.attributes.start);
      const endTime = session.attributes.stop ? new Date(session.attributes.stop) : new Date();

      const durationMs = endTime.getTime() - startTime.getTime();
      const durationHours = Math.max(0, durationMs / (1000 * 60 * 60)); // Ensure positive hours

      totalHours += durationHours;
    });

    return totalHours;
  }

  calculateOrgServerHours(sessions: PlayerSession[], orgServers: any[]): ServerInfo[] {
    const orgServerIds = orgServers.map(server => server.id);
    const serverHours: { [serverId: string]: number } = {};

    console.log(`Calculating hours for ${orgServers.length} org servers:`, orgServerIds);
    console.log(`Processing ${sessions.length} sessions`);

    // Initialize all org servers with 0 hours
    orgServerIds.forEach(serverId => {
      serverHours[serverId] = 0;
    });

    sessions.forEach((session, index) => {
      const serverId = session.relationships?.server?.data?.id;

      if (index < 5) { // Log first 5 sessions for debugging
        console.log(`Session ${index}: Server ID ${serverId}, Start: ${session.attributes.start}`);
      }

      // Only count hours for org servers
      if (!serverId || !orgServerIds.includes(serverId)) {
        if (index < 5) console.log(`  -> Skipped (not in org servers)`);
        return;
      }

      const startTime = new Date(session.attributes.start);
      const endTime = session.attributes.stop ? new Date(session.attributes.stop) : new Date();

      const durationMs = endTime.getTime() - startTime.getTime();
      const durationHours = Math.max(0, durationMs / (1000 * 60 * 60)); // Ensure positive hours

      serverHours[serverId] = (serverHours[serverId] || 0) + durationHours;

      if (index < 5) console.log(`  -> Added ${durationHours.toFixed(2)} hours to server ${serverId}`);
    });

    console.log('Final server hours:', serverHours);

    // Convert to ServerInfo array with names - include all servers, even with 0 hours for debugging
    const result = orgServers.map(server => ({
      id: server.id,
      name: server.attributes?.name || `Server ${server.id}`,
      hours: serverHours[server.id] || 0
    }));

    console.log('Server info result:', result);

    return result.filter(server => server.hours > 0); // Only include servers with hours
  }

  calculateKillsAndDeaths(activities: ActivityLogEntry[]): { kills: number; deaths: number } {
    let kills = 0;
    let deaths = 0;

    activities.forEach(activity => {
      if (activity.type === 'rustKill') {
        kills++;
      } else if (activity.type === 'rustDeath') {
        deaths++;
      }
    });

    return { kills, deaths };
  }

  async getPlayerStats(playerIdentifier: string): Promise<PlayerStats | null> {
    try {
      let searchResult;

      // Check if the identifier looks like a Steam ID (17 digits)
      if (/^\d{17}$/.test(playerIdentifier)) {
        console.log(`Searching by Steam ID: ${playerIdentifier}`);
        searchResult = await this.findPlayerBySteamId(playerIdentifier);
      } else {
        console.log(`Searching by player name: ${playerIdentifier}`);
        searchResult = await this.findPlayerByName(playerIdentifier);
      }

      if (!searchResult.data || searchResult.data.length === 0) {
        return null;
      }

      const player = searchResult.data[0];
      const playerId = player.id;

      console.log(`Found player: ${player.attributes.name} (ID: ${playerId})`);

      // Get sessions, activity, and org servers in parallel
      const [sessions, activities, orgServers] = await Promise.all([
        this.getPlayerSessions(playerId),
        this.getPlayerActivity(playerId),
        this.getOrganizationServers()
      ]);

      // Calculate stats
      const totalHours = this.calculateTotalHours(sessions);
      const orgServerHours = this.calculateOrgServerHours(sessions, orgServers);
      const { kills, deaths } = this.calculateKillsAndDeaths(activities);
      const killDeathRatio = deaths > 0 ? kills / deaths : kills;

      console.log(`Total hours calculated: ${totalHours.toFixed(1)}`);
      console.log(`Organization servers found: ${orgServerHours.length}`);

      return {
        playerId,
        playerName: player.attributes.name,
        totalHours,
        orgServers: orgServerHours,
        kills,
        deaths,
        killDeathRatio,
        recentActivity: activities.slice(0, 10) // Last 10 activities
      };

    } catch (error) {
      console.error('Error fetching player stats:', error);
      throw error;
    }
  }
}
