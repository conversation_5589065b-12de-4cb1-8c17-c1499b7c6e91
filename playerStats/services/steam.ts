interface SteamConfig {
  apiKey: string;
  rustAppId: string;
}

interface SettingsConfig {
  cacheTimeMinutes: number;
  rateLimitMs: number;
}

interface SteamPlayer {
  steamid: string;
  personaname: string;
  profileurl: string;
  avatar: string;
  avatarmedium: string;
  avatarfull: string;
}

interface SteamGameStats {
  appid: number;
  playtime_forever: number;
  playtime_2weeks?: number;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

export class SteamAPI {
  private config: SteamConfig;
  private settings: SettingsConfig;
  private cache: Map<string, CacheEntry<any>>;
  private lastRequestTime: number = 0;

  constructor(config: SteamConfig, settings: SettingsConfig) {
    this.config = config;
    this.settings = settings;
    this.cache = new Map();
  }

  private async rateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.settings.rateLimitMs) {
      const waitTime = this.settings.rateLimitMs - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  private isCacheValid<T>(cacheKey: string): boolean {
    const cached = this.cache.get(cacheKey);
    if (!cached) return false;

    const cacheAge = Date.now() - cached.timestamp;
    const maxAge = this.settings.cacheTimeMinutes * 60 * 1000;

    return cacheAge < maxAge;
  }

  private getCached<T>(cacheKey: string): T | null {
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey)!.data;
    }
    return null;
  }

  private setCache<T>(cacheKey: string, data: T): void {
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  private async makeRequest(url: string): Promise<any> {
    await this.rateLimit();

    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'PlayerStatsBot/1.0'
      }
    });

    if (!response.ok) {
      throw new Error(`Steam API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  async findPlayerBySteamId(steamId: string): Promise<SteamPlayer | null> {
    const cacheKey = `steam_player_${steamId}`;
    const cached = this.getCached<SteamPlayer>(cacheKey);
    if (cached) return cached;

    try {
      const url = `https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v0002/?key=${this.config.apiKey}&steamids=${steamId}`;
      const response = await this.makeRequest(url);

      if (response.response?.players?.length > 0) {
        const player = response.response.players[0];
        this.setCache(cacheKey, player);
        return player;
      }

      return null;
    } catch (error) {
      console.error('Error fetching Steam player:', error);
      return null;
    }
  }

  async findPlayerByVanityUrl(vanityUrl: string): Promise<string | null> {
    const cacheKey = `steam_vanity_${vanityUrl}`;
    const cached = this.getCached<string>(cacheKey);
    if (cached) return cached;

    try {
      const url = `https://api.steampowered.com/ISteamUser/ResolveVanityURL/v0001/?key=${this.config.apiKey}&vanityurl=${vanityUrl}`;
      const response = await this.makeRequest(url);

      if (response.response?.success === 1) {
        const steamId = response.response.steamid;
        this.setCache(cacheKey, steamId);
        return steamId;
      }

      return null;
    } catch (error) {
      console.error('Error resolving vanity URL:', error);
      return null;
    }
  }

  async getPlayerGameStats(steamId: string): Promise<number | null> {
    const cacheKey = `steam_rust_hours_${steamId}`;
    const cached = this.getCached<number>(cacheKey);
    if (cached !== null) return cached;

    try {
      // First try the user stats API
      const statsUrl = `https://api.steampowered.com/ISteamUserStats/GetUserStatsForGame/v0002/?appid=${this.config.rustAppId}&key=${this.config.apiKey}&steamid=${steamId}&include_appinfo=1`;
      const statsResponse = await this.makeRequest(statsUrl);

      if (statsResponse.playerstats?.stats) {
        console.log(`Found ${statsResponse.playerstats.stats.length} stats for Steam ID ${steamId}`);

        // Look for the total playtime stat - try multiple possible names
        const possibleNames = [
          'total_time_played',
          'seconds_played',
          'playtime_seconds',
          'lifetime_seconds',
          'total_playtime',
          'playtime_total'
        ];

        let playtimeStat = null;
        for (const name of possibleNames) {
          playtimeStat = statsResponse.playerstats.stats.find((stat: any) => stat.name === name);
          if (playtimeStat) {
            console.log(`Found playtime stat: ${name} = ${playtimeStat.value}`);
            const hours = playtimeStat.value / 3600;
            this.setCache(cacheKey, hours);
            return hours;
          }
        }

        // If we didn't find a specific playtime stat, log available stats for debugging
        console.log('Available stats:', statsResponse.playerstats.stats.map((s: any) => s.name).slice(0, 10));
        console.log('No playtime stat found in user stats, trying owned games API...');
      }

      // Fallback to owned games API for playtime
      const gamesUrl = `https://api.steampowered.com/IPlayerService/GetOwnedGames/v0001/?key=${this.config.apiKey}&steamid=${steamId}&format=json&include_appinfo=false`;
      const gamesResponse = await this.makeRequest(gamesUrl);

      if (gamesResponse.response?.games) {
        const rustGame = gamesResponse.response.games.find((game: any) =>
          game.appid.toString() === this.config.rustAppId
        );

        if (rustGame && rustGame.playtime_forever) {
          // Convert minutes to hours
          const hours = rustGame.playtime_forever / 60;
          console.log(`Found Rust playtime from owned games: ${hours} hours`);
          this.setCache(cacheKey, hours);
          return hours;
        }
      }

      console.log('No playtime data found in either API');
      return null;
    } catch (error) {
      console.error('Error fetching Steam game stats:', error);
      return null;
    }
  }

  async getRustHours(playerIdentifier: string): Promise<number | null> {
    try {
      let steamId = playerIdentifier;

      // Check if it's a vanity URL or Steam ID
      if (!/^\d{17}$/.test(playerIdentifier)) {
        // Try to resolve as vanity URL
        const resolvedId = await this.findPlayerByVanityUrl(playerIdentifier);
        if (!resolvedId) return null;
        steamId = resolvedId;
      }

      const hours = await this.getPlayerGameStats(steamId);
      return hours;
    } catch (error) {
      console.error('Error getting Rust hours:', error);
      return null;
    }
  }

  async getPlayerInfo(playerIdentifier: string): Promise<{ player: SteamPlayer; rustHours: number } | null> {
    try {
      let steamId = playerIdentifier;

      // Check if it's a vanity URL or Steam ID
      if (!/^\d{17}$/.test(playerIdentifier)) {
        // Try to resolve as vanity URL
        const resolvedId = await this.findPlayerByVanityUrl(playerIdentifier);
        if (!resolvedId) return null;
        steamId = resolvedId;
      }

      const [player, rustHours] = await Promise.all([
        this.findPlayerBySteamId(steamId),
        this.getPlayerGameStats(steamId)
      ]);

      if (!player || rustHours === null) return null;

      return {
        player,
        rustHours
      };
    } catch (error) {
      console.error('Error getting player info:', error);
      return null;
    }
  }
}
