import { PlayerStats, IPlayerStats } from '../models/PlayerStats.js';
import { BattlemetricsAPI } from './battlemetrics.js';
import { SteamAPI } from './steam.js';

export interface PlayerStatsCreateInput {
  steamId: string;
  forceRefresh?: boolean;
}

export interface PlayerStatsUpdateInput {
  playerName?: string;
  totalHours?: number;
  kills?: number;
  deaths?: number;
  orgServers?: Array<{
    id: string;
    name: string;
    hours: number;
  }>;
}

export class PlayerStatsService {
  private battlemetricsAPI: BattlemetricsAPI;
  private steamAPI: SteamAPI;

  constructor(battlemetricsAPI: BattlemetricsAPI, steamAPI: SteamAPI) {
    this.battlemetricsAPI = battlemetricsAPI;
    this.steamAPI = steamAPI;
  }

  async getPlayerStats(steamId: string): Promise<IPlayerStats | null> {
    try {
      const playerStats = await PlayerStats.findOne({ steamId });
      return playerStats;
    } catch (error) {
      console.error('Error fetching player stats from database:', error);
      throw new Error('Failed to fetch player stats');
    }
  }

  async createOrUpdatePlayerStats(input: PlayerStatsCreateInput): Promise<IPlayerStats> {
    const { steamId, forceRefresh = false } = input;

    try {
      // Check if player already exists and if we should use cached data
      const existingPlayer = await PlayerStats.findOne({ steamId });
      
      if (existingPlayer && !forceRefresh) {
        // Check if data is recent (less than 30 minutes old)
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
        if (existingPlayer.lastUpdated > thirtyMinutesAgo) {
          console.log(`Using cached data for Steam ID: ${steamId}`);
          return existingPlayer;
        }
      }

      // Fetch fresh data from APIs
      console.log(`Fetching fresh data for Steam ID: ${steamId}`);
      const battlemetricsData = await this.battlemetricsAPI.getPlayerStatsBySteamId(steamId);
      
      if (!battlemetricsData) {
        throw new Error(`Player not found on Battlemetrics for Steam ID: ${steamId}`);
      }

      // Prepare the data for database
      const playerData = {
        steamId,
        playerId: battlemetricsData.playerId,
        playerName: battlemetricsData.playerName,
        totalHours: battlemetricsData.totalHours,
        orgServers: battlemetricsData.orgServers,
        kills: battlemetricsData.kills,
        deaths: battlemetricsData.deaths,
        killDeathRatio: battlemetricsData.killDeathRatio,
        recentActivity: battlemetricsData.recentActivity
      };

      // Update or create player stats
      const updatedPlayer = await PlayerStats.findOneAndUpdate(
        { steamId },
        playerData,
        { 
          new: true, 
          upsert: true,
          runValidators: true
        }
      );

      console.log(`Successfully updated player stats for: ${playerData.playerName}`);
      return updatedPlayer!;

    } catch (error) {
      console.error('Error creating/updating player stats:', error);
      throw error;
    }
  }

  async updatePlayerStats(steamId: string, updates: PlayerStatsUpdateInput): Promise<IPlayerStats | null> {
    try {
      const updatedPlayer = await PlayerStats.findOneAndUpdate(
        { steamId },
        { 
          ...updates,
          lastUpdated: new Date()
        },
        { 
          new: true,
          runValidators: true
        }
      );

      if (!updatedPlayer) {
        throw new Error(`Player not found with Steam ID: ${steamId}`);
      }

      console.log(`Manually updated player stats for: ${updatedPlayer.playerName}`);
      return updatedPlayer;

    } catch (error) {
      console.error('Error updating player stats:', error);
      throw error;
    }
  }

  async deletePlayerStats(steamId: string): Promise<boolean> {
    try {
      const result = await PlayerStats.deleteOne({ steamId });
      
      if (result.deletedCount === 0) {
        return false;
      }

      console.log(`Deleted player stats for Steam ID: ${steamId}`);
      return true;

    } catch (error) {
      console.error('Error deleting player stats:', error);
      throw error;
    }
  }

  async getAllPlayerStats(limit: number = 50, offset: number = 0): Promise<IPlayerStats[]> {
    try {
      const players = await PlayerStats.find()
        .sort({ lastUpdated: -1 })
        .limit(limit)
        .skip(offset);

      return players;

    } catch (error) {
      console.error('Error fetching all player stats:', error);
      throw error;
    }
  }

  async searchPlayersByName(name: string, limit: number = 10): Promise<IPlayerStats[]> {
    try {
      const players = await PlayerStats.find({
        playerName: { $regex: name, $options: 'i' }
      })
        .sort({ lastUpdated: -1 })
        .limit(limit);

      return players;

    } catch (error) {
      console.error('Error searching players by name:', error);
      throw error;
    }
  }

  async getPlayerStatsWithPagination(page: number = 1, limit: number = 20) {
    try {
      const offset = (page - 1) * limit;
      const total = await PlayerStats.countDocuments();
      const players = await this.getAllPlayerStats(limit, offset);

      return {
        players,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      console.error('Error fetching paginated player stats:', error);
      throw error;
    }
  }
}
