import { Client, GatewayIntentBits, Collection } from 'discord.js';
import { readFileSync } from 'fs';
import { join } from 'path';
import { StatsCommand } from './commands/player.js';
import { BattlemetricsAPI } from './services/battlemetrics.js';
import { SteamAPI } from './services/steam.js';
import { PlayerStatsAPI } from './api/server.js';
import { DatabaseService } from './services/database.js';

interface Config {
  discord: {
    token: string;
    prefix: string;
  };
  steam: {
    apiKey: string;
    rustAppId: string;
  };
  battlemetrics: {
    orgId: string;
    apiUrl: string;
    apiToken?: string;
    serverIds?: string[];
    servers?: Array<{
      id: string;
      name: string;
    }>;
  };
  settings: {
    killDeathDays: number;
    cacheTimeMinutes: number;
    rateLimitMs: number;
  };
  api?: {
    port: number;
    mongoUrl: string;
  };
  emojis?: {
    battlemetrics?: string;
    rust?: string;
  };
}

class PlayerStatsBot {
  private client: Client;
  private config: Config;
  private battlemetricsAPI: BattlemetricsAPI;
  private steamAPI: SteamAPI;
  private commands: Collection<string, any>;
  private apiServer: PlayerStatsAPI | null = null;
  private databaseService: DatabaseService;

  constructor() {
    this.config = JSON.parse(readFileSync(join(process.cwd(), 'config.json'), 'utf-8'));

    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
      ]
    });

    this.battlemetricsAPI = new BattlemetricsAPI(this.config.battlemetrics, this.config.settings);
    this.steamAPI = new SteamAPI(this.config.steam, this.config.settings);
    this.commands = new Collection();
    this.databaseService = DatabaseService.getInstance();

    this.setupCommands();
    this.setupEventHandlers();
  }

  private setupCommands() {
    const statsCommand = new StatsCommand(this.battlemetricsAPI, this.steamAPI, this.config);
    this.commands.set('player', statsCommand);
    this.commands.set('stats', statsCommand); // Add 'stats' as an alias
  }

  private setupEventHandlers() {
    this.client.once('ready', async () => {
      console.log(`Bot is ready! Logged in as ${this.client.user?.tag}`);
      console.log(`Monitoring organization: ${this.config.battlemetrics.orgId}`);
      console.log(`Using prefix: ${this.config.discord.prefix}`);

      // Start the API server
      await this.startAPIServer();
    });

    this.client.on('messageCreate', async (message) => {
      // Ignore bot messages
      if (message.author.bot) return;

      // Check if message starts with prefix
      if (!message.content.startsWith(this.config.discord.prefix)) return;

      // Parse command and arguments
      const args = message.content.slice(this.config.discord.prefix.length).trim().split(/ +/);
      const commandName = args.shift()?.toLowerCase();

      if (!commandName) return;

      console.log(`Command received: "${commandName}" with args:`, args);

      // Get command
      const command = this.commands.get(commandName);
      if (!command) {
        console.log(`Command "${commandName}" not found. Available commands:`, Array.from(this.commands.keys()));
        return;
      }

      try {
        await command.execute(message, args);
      } catch (error) {
        console.error('Error executing command:', error);
        await message.reply('There was an error while executing this command!');
      }
    });
  }

  private async startAPIServer() {
    try {
      // Use MongoDB URL from config or default
      const mongoUrl = this.config.api?.mongoUrl || '****************************************************';
      const port = this.config.api?.port || 3000;

      // Create API configuration
      const apiConfig = {
        port,
        mongoUrl,
        battlemetrics: this.config.battlemetrics,
        steam: this.config.steam,
        settings: this.config.settings
      };

      // Initialize and start the API server
      this.apiServer = new PlayerStatsAPI(apiConfig);
      await this.apiServer.start(port);

      console.log(`Player Stats API started on http://localhost:${port}`);
      console.log(`API endpoints available for plugin integration`);

    } catch (error) {
      console.error('Failed to start API server:', error);
      console.log('Discord bot will continue without API server');
    }
  }

  async start() {
    try {
      await this.client.login(this.config.discord.token);
    } catch (error) {
      console.error('Error starting bot:', error);
      process.exit(1);
    }
  }

  async stop() {
    try {
      console.log('Shutting down Player Stats Bot...');

      // Disconnect from Discord
      this.client.destroy();

      // Disconnect from database
      await this.databaseService.disconnect();

      console.log('Player Stats Bot stopped gracefully');
    } catch (error) {
      console.error('Error during shutdown:', error);
    }
  }
}

// Start the bot
const bot = new PlayerStatsBot();
bot.start().catch(console.error);

// Graceful shutdown handlers
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT, shutting down gracefully...');
  await bot.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...');
  await bot.stop();
  process.exit(0);
});
