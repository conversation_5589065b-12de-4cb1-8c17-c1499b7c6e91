import { Client, GatewayIntentBits, Collection, REST, Routes } from 'discord.js';
import { readFileSync } from 'fs';
import { join } from 'path';
import { PlayerStatsCommand } from './commands/playerstats.js';
import { BattlemetricsAPI } from './services/battlemetrics.js';
import { ServerInfoService } from './services/serverInfo.js';

interface Config {
  discord: {
    token: string;
    clientId: string;
    guildId: string;
  };
  battlemetrics: {
    serverIds: string[];
    apiUrl: string;
  };
  settings: {
    killDeathDays: number;
    cacheTimeMinutes: number;
    rateLimitMs: number;
  };
}

class PlayerStatsBot {
  private client: Client;
  private config: Config;
  private battlemetricsAPI: BattlemetricsAPI;
  private serverInfoService: ServerInfoService;
  private commands: Collection<string, any>;

  constructor() {
    this.config = JSON.parse(readFileSync(join(process.cwd(), 'config.json'), 'utf-8'));

    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
      ]
    });

    this.battlemetricsAPI = new BattlemetricsAPI(this.config.battlemetrics, this.config.settings);
    this.serverInfoService = new ServerInfoService(this.config.battlemetrics.apiUrl, this.config.settings.cacheTimeMinutes);
    this.commands = new Collection();

    this.setupCommands();
    this.setupEventHandlers();
  }

  private setupCommands() {
    const playerStatsCommand = new PlayerStatsCommand(this.battlemetricsAPI, this.serverInfoService);
    this.commands.set(playerStatsCommand.data.name, playerStatsCommand);
  }

  private setupEventHandlers() {
    this.client.once('ready', () => {
      console.log(`✅ Bot is ready! Logged in as ${this.client.user?.tag}`);
      console.log(`📊 Monitoring ${this.config.battlemetrics.serverIds.length} servers`);
    });

    this.client.on('interactionCreate', async (interaction) => {
      if (!interaction.isChatInputCommand()) return;

      const command = this.commands.get(interaction.commandName);
      if (!command) return;

      try {
        await command.execute(interaction);
      } catch (error) {
        console.error('Error executing command:', error);
        const reply = {
          content: 'There was an error while executing this command!',
          ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp(reply);
        } else {
          await interaction.reply(reply);
        }
      }
    });
  }

  async deployCommands() {
    const commands = Array.from(this.commands.values()).map(command => command.data.toJSON());

    const rest = new REST().setToken(this.config.discord.token);

    try {
      console.log('🔄 Started refreshing application (/) commands.');

      await rest.put(
        Routes.applicationGuildCommands(this.config.discord.clientId, this.config.discord.guildId),
        { body: commands }
      );

      console.log('✅ Successfully reloaded application (/) commands.');
    } catch (error) {
      console.error('❌ Error deploying commands:', error);
    }
  }

  async start() {
    try {
      await this.deployCommands();
      await this.client.login(this.config.discord.token);
    } catch (error) {
      console.error('❌ Error starting bot:', error);
      process.exit(1);
    }
  }
}

// Start the bot
const bot = new PlayerStatsBot();
bot.start().catch(console.error);
