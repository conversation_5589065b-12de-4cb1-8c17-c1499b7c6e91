import { Client, GatewayIntentBits, Collection } from 'discord.js';
import { readFileSync } from 'fs';
import { join } from 'path';
import { StatsCommand } from './commands/stats.js';
import { BattlemetricsAPI } from './services/battlemetrics.js';
import { SteamAPI } from './services/steam.js';

interface Config {
  discord: {
    token: string;
    prefix: string;
  };
  steam: {
    apiKey: string;
    rustAppId: string;
  };
  battlemetrics: {
    orgId: string;
    apiUrl: string;
  };
  settings: {
    killDeathDays: number;
    cacheTimeMinutes: number;
    rateLimitMs: number;
  };
}

class PlayerStatsBot {
  private client: Client;
  private config: Config;
  private battlemetricsAPI: BattlemetricsAPI;
  private steamAPI: SteamAPI;
  private commands: Collection<string, any>;

  constructor() {
    this.config = JSON.parse(readFileSync(join(process.cwd(), 'config.json'), 'utf-8'));

    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
      ]
    });

    this.battlemetricsAPI = new BattlemetricsAPI(this.config.battlemetrics, this.config.settings);
    this.steamAPI = new SteamAPI(this.config.steam, this.config.settings);
    this.commands = new Collection();

    this.setupCommands();
    this.setupEventHandlers();
  }

  private setupCommands() {
    const statsCommand = new StatsCommand(this.battlemetricsAPI, this.steamAPI);
    this.commands.set('stats', statsCommand);
  }

  private setupEventHandlers() {
    this.client.once('ready', () => {
      console.log(`✅ Bot is ready! Logged in as ${this.client.user?.tag}`);
      console.log(`📊 Monitoring organization: ${this.config.battlemetrics.orgId}`);
      console.log(`🎮 Using prefix: ${this.config.discord.prefix}`);
    });

    this.client.on('messageCreate', async (message) => {
      // Ignore bot messages
      if (message.author.bot) return;

      // Check if message starts with prefix
      if (!message.content.startsWith(this.config.discord.prefix)) return;

      // Parse command and arguments
      const args = message.content.slice(this.config.discord.prefix.length).trim().split(/ +/);
      const commandName = args.shift()?.toLowerCase();

      if (!commandName) return;

      // Get command
      const command = this.commands.get(commandName);
      if (!command) return;

      try {
        await command.execute(message, args);
      } catch (error) {
        console.error('Error executing command:', error);
        await message.reply('❌ There was an error while executing this command!');
      }
    });
  }

  async start() {
    try {
      await this.client.login(this.config.discord.token);
    } catch (error) {
      console.error('❌ Error starting bot:', error);
      process.exit(1);
    }
  }
}

// Start the bot
const bot = new PlayerStatsBot();
bot.start().catch(console.error);
