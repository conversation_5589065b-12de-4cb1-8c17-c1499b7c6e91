# Player Stats API

A RESTful API built with Elysia and Mongoose for managing Rust player statistics. This API integrates with Battlemetrics and Steam APIs to fetch and store player data in MongoDB.

## Features

- **Steam ID-based player lookup** - Primary key for all operations
- **Automatic K/D ratio calculation** - Calculated and stored automatically
- **Battlemetrics integration** - Fetches playtime and server data
- **MongoDB storage** - Persistent data storage with Mongoose ODM
- **Caching system** - 30-minute cache to reduce API calls
- **RESTful endpoints** - Full CRUD operations
- **Input validation** - Type-safe with Elysia validation
- **Pagination support** - Efficient data retrieval

## Installation & Setup

1. **Install dependencies** (already done):
   ```bash
   bun add elysia mongoose @types/mongoose
   ```

2. **Configure MongoDB** in `config.json`:
   ```json
   {
     "api": {
       "port": 3000,
       "mongoUrl": "***************************************************/"
     }
   }
   ```

3. **Start the API server**:
   ```bash
   # Development mode with auto-reload
   bun run api:dev
   
   # Production mode
   bun run api:start
   ```

## API Endpoints

### Base URL
```
http://localhost:3000
```

### 1. Get API Information
```http
GET /
```
Returns API information and available endpoints.

### 2. Get Player Stats by Steam ID
```http
GET /api/player/{steamId}
```
**Parameters:**
- `steamId` (string, required): 17-digit Steam ID

**Response:**
```json
{
  "success": true,
  "data": {
    "steamId": "76561199182780054",
    "playerId": "123456789",
    "playerName": "Bob the Builder",
    "totalHours": 245.5,
    "orgServers": [
      {
        "id": "33665102",
        "name": "Oasis Rust Server",
        "hours": 123.2
      }
    ],
    "kills": 45,
    "deaths": 23,
    "killDeathRatio": 1.96,
    "recentActivity": [...],
    "lastUpdated": "2025-01-25T10:30:00.000Z",
    "createdAt": "2025-01-20T15:20:00.000Z"
  }
}
```

### 3. Create/Update Player Stats
```http
POST /api/player
```
**Body:**
```json
{
  "steamId": "76561199182780054",
  "forceRefresh": false
}
```
- `steamId` (string, required): 17-digit Steam ID
- `forceRefresh` (boolean, optional): Force fresh API data fetch

### 4. Update Existing Player Stats
```http
PUT /api/player/{steamId}
```
**Body:**
```json
{
  "playerName": "New Name",
  "totalHours": 300.5,
  "kills": 50,
  "deaths": 25,
  "orgServers": [
    {
      "id": "33665102",
      "name": "Server Name",
      "hours": 150.0
    }
  ]
}
```

### 5. Delete Player Stats
```http
DELETE /api/player/{steamId}
```

### 6. Get All Players (Paginated)
```http
GET /api/players?page=1&limit=20
```
**Query Parameters:**
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 20, max: 100)

### 7. Search Players by Name
```http
GET /api/search?name=Bob&limit=10
```
**Query Parameters:**
- `name` (string, required): Player name to search (min 2 characters)
- `limit` (number, optional): Max results (default: 10, max: 50)

## Data Model

### PlayerStats Schema
```typescript
{
  steamId: string;           // Primary key, 17-digit Steam ID
  playerId: string;          // Battlemetrics player ID
  playerName: string;        // Player display name
  totalHours: number;        // Total playtime across all servers
  orgServers: [              // Organization server breakdown
    {
      id: string;            // Server ID
      name: string;          // Server name
      hours: number;         // Hours on this server
    }
  ];
  kills: number;             // Total kills
  deaths: number;            // Total deaths
  killDeathRatio: number;    // Automatically calculated K/D ratio
  recentActivity: [...];     // Recent activity log entries
  lastUpdated: Date;         // Last data refresh timestamp
  createdAt: Date;           // Record creation timestamp
}
```

## Usage Examples

### JavaScript/TypeScript
```typescript
// Fetch player stats
const response = await fetch('http://localhost:3000/api/player/76561199182780054');
const data = await response.json();

// Create/update player
const createResponse = await fetch('http://localhost:3000/api/player', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    steamId: '76561199182780054',
    forceRefresh: true
  })
});

// Search players
const searchResponse = await fetch('http://localhost:3000/api/search?name=Bob');
const searchData = await searchResponse.json();
```

### cURL Examples
```bash
# Get player stats
curl http://localhost:3000/api/player/76561199182780054

# Create/update player
curl -X POST http://localhost:3000/api/player \
  -H "Content-Type: application/json" \
  -d '{"steamId":"76561199182780054","forceRefresh":true}'

# Search players
curl "http://localhost:3000/api/search?name=Bob&limit=5"
```

## Error Handling

All endpoints return consistent error responses:
```json
{
  "success": false,
  "error": "Error description",
  "details": "Additional error details (if available)"
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request (validation error)
- `404` - Not Found
- `500` - Internal Server Error

## Caching

- Player data is cached for 30 minutes
- Use `forceRefresh: true` to bypass cache
- Cache automatically expires and refreshes stale data

## Integration with Discord Bot

The API can be used alongside the existing Discord bot:

1. **Discord bot** - Interactive commands for users
2. **API** - Programmatic access for plugins/external tools
3. **Shared database** - Both use the same MongoDB instance

## Development

### File Structure
```
playerStats/
├── api/
│   ├── index.ts           # API startup script
│   └── server.ts          # Elysia server setup
├── models/
│   └── PlayerStats.ts     # Mongoose schema
├── services/
│   ├── database.ts        # Database connection
│   ├── playerStatsService.ts  # Business logic
│   ├── battlemetrics.ts   # Battlemetrics API
│   └── steam.ts           # Steam API
└── config.json            # Configuration
```

### Running in Development
```bash
# Start API with auto-reload
bun run api:dev

# Start Discord bot
bun run dev

# Both can run simultaneously on different ports
```
