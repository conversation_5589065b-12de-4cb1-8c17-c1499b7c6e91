# Player Stats API

A RESTful API built with Elysia and Mongoose for managing Rust player statistics. This API integrates with Battlemetrics and Steam APIs to fetch and store player data in MongoDB.

## Features

- **Steam ID-based player lookup** - Primary key for all operations
- **Daily stats tracking** - Track kills, deaths, and playtime per day
- **14-day rolling window** - Automatically cleans up data older than 14 days
- **Automatic K/D ratio calculation** - Calculated from daily stats
- **Custom server names** - Set meaningful names for each server ID
- **Battlemetrics integration** - Fetches playtime and server data
- **MongoDB storage** - Persistent data storage with Mongoose ODM
- **Plugin-friendly endpoints** - Easy integration for game plugins
- **RESTful endpoints** - Full CRUD operations
- **Input validation** - Type-safe with Elysia validation
- **Real-time data tracking** - Record events as they happen

## Installation & Setup

1. **Install dependencies** (already done):
   ```bash
   bun add elysia mongoose @types/mongoose
   ```

2. **Configure MongoDB and servers** in `config.json`:
   ```json
   {
     "battlemetrics": {
       "servers": [
         {
           "id": "33665102",
           "name": "Oasis Rust Main"
         },
         {
           "id": "12345678",
           "name": "Oasis Rust PvE"
         }
       ]
     },
     "api": {
       "port": 3000,
       "mongoUrl": "***************************************************/"
     }
   }
   ```

3. **Start the system** (Discord bot + API):
   ```bash
   # Both Discord bot and API start together
   bun ./index.ts
   ```

## Custom Server Names

The new server configuration allows you to set custom names for each server:

### Before (Generic Names)
```
Server 33665102: 45.2h
```

### After (Custom Names)
```
Oasis Rust Main: 45.2h
```

### Configuration
```json
{
  "battlemetrics": {
    "servers": [
      {
        "id": "33665102",
        "name": "Oasis Rust Main"
      },
      {
        "id": "12345678",
        "name": "Oasis Rust PvE"
      }
    ]
  }
}
```

## API Endpoints

### Base URL
```
http://localhost:3000
```

### 1. Get API Information
```http
GET /
```

### 2. Get Player Stats by Steam ID
```http
GET /api/player/{steamId}
```

**Response with Custom Server Names:**
```json
{
  "success": true,
  "data": {
    "steamId": "76561199182780054",
    "playerName": "Bob the Builder",
    "totalHours": 245.5,
    "totalOrgHours": 123.2,
    "orgServers": [
      {
        "id": "33665102",
        "name": "Oasis Rust Main",
        "hours": 123.2
      }
    ],
    "kills": 45,
    "deaths": 23,
    "killDeathRatio": 1.96
  }
}
```

**Field Explanations:**
- `totalHours`: Total Rust hours from all servers (Steam/Battlemetrics)
- `totalOrgHours`: Sum of hours from organization servers only
- `orgServers`: Individual server breakdown with custom names

### 3. Create/Update Player Stats
```http
POST /api/player
```
**Body:**
```json
{
  "steamId": "76561199182780054",
  "forceRefresh": false
}
```

### 4. Update Existing Player Stats
```http
PUT /api/player/{steamId}
```

### 5. Delete Player Stats
```http
DELETE /api/player/{steamId}
```

### 6. Get All Players (Paginated)
```http
GET /api/players?page=1&limit=20
```

### 7. Search Players by Name
```http
GET /api/search?name=Bob&limit=10
```

## Daily Stats Endpoints (Plugin Integration)

### 8. Record a Kill Event
```http
POST /api/daily/kill
```
**Body:**
```json
{
  "steamId": "76561199182780054",
  "activity": {
    "id": "kill_123456",
    "type": "kill",
    "timestamp": "2025-01-25T10:30:00.000Z",
    "data": {
      "killer": {
        "name": "Bob the Builder",
        "id": "76561199182780054"
      },
      "victim": {
        "name": "Target Player",
        "id": "76561198000000000"
      },
      "weapon": "AK47",
      "distance": 150.5
    }
  }
}
```

### 9. Record a Death Event
```http
POST /api/daily/death
```
**Body:**
```json
{
  "steamId": "76561199182780054",
  "activity": {
    "id": "death_123456",
    "type": "death",
    "timestamp": "2025-01-25T10:30:00.000Z",
    "data": {
      "killer": {
        "name": "Enemy Player",
        "id": "76561198000000000"
      },
      "victim": {
        "name": "Bob the Builder",
        "id": "76561199182780054"
      },
      "weapon": "Bolt Action Rifle",
      "distance": 200.0
    }
  }
}
```

### 10. Record Playtime Session
```http
POST /api/daily/playtime
```
**Body:**
```json
{
  "steamId": "76561199182780054",
  "serverId": "33665102",
  "serverName": "Oasis Rust Main",
  "minutes": 60
}
```

### 11. Get Daily Stats for Player
```http
GET /api/daily/{steamId}?startDate=2025-01-20&endDate=2025-01-25
```
**Response:**
```json
{
  "success": true,
  "data": [
    {
      "date": "2025-01-25",
      "kills": 5,
      "deaths": 2,
      "playtimeMinutes": 120,
      "serverSessions": [
        {
          "serverId": "33665102",
          "serverName": "Oasis Rust Main",
          "minutes": 120
        }
      ],
      "activities": [...]
    }
  ],
  "count": 1
}
```

### 12. Get Aggregated Stats (Last N Days)
```http
GET /api/daily/{steamId}/aggregated?days=14
```
**Response:**
```json
{
  "success": true,
  "data": {
    "totalKills": 45,
    "totalDeaths": 23,
    "totalPlaytimeMinutes": 1800,
    "killDeathRatio": 1.96,
    "dailyBreakdown": [...]
  },
  "period": "Last 14 days"
}
```

## Usage Examples

### JavaScript/TypeScript
```typescript
// Get player stats
const response = await fetch('http://localhost:3000/api/player/76561199182780054');
const data = await response.json();

// Create/update player
const createResponse = await fetch('http://localhost:3000/api/player', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    steamId: '76561199182780054',
    forceRefresh: true
  })
});
```

### cURL Examples
```bash
# Get player stats
curl http://localhost:3000/api/player/76561199182780054

# Create/update player
curl -X POST http://localhost:3000/api/player \
  -H "Content-Type: application/json" \
  -d '{"steamId":"76561199182780054","forceRefresh":true}'
```

## Integration with Discord Bot

The API runs automatically when you start the Discord bot:

1. **Start the system:**
   ```bash
   cd playerStats
   bun ./index.ts
   ```

2. **Both services run together:**
   - Discord bot: Available in Discord with `,player {steamId}` command
   - API server: Available at `http://localhost:3000`

3. **Shared database:** Both use the same MongoDB instance

## Data Flow

```
Plugin → API → MongoDB ← Discord Bot
   ↓        ↓       ↓         ↓
 Events   Store   Persist   Display
```

## Error Handling

All endpoints return consistent error responses:
```json
{
  "success": false,
  "error": "Error description",
  "details": "Additional error details (if available)"
}
```

## Development

### File Structure
```
playerStats/
├── api/
│   ├── index.ts           # API startup script
│   └── server.ts          # Elysia server setup
├── models/
│   └── PlayerStats.ts     # Mongoose schema
├── services/
│   ├── database.ts        # Database connection
│   ├── playerStatsService.ts  # Business logic
│   ├── battlemetrics.ts   # Battlemetrics API
│   └── steam.ts           # Steam API
└── config.json            # Configuration
```

### Running in Development
```bash
# Start both Discord bot and API
bun ./index.ts

# The API will be available at http://localhost:3000
# The Discord bot will be available in Discord
```
