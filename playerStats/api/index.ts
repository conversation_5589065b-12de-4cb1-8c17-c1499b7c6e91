import { readFileSync } from 'fs';
import { join } from 'path';
import { PlayerStatsAPI } from './server.js';

interface Config {
  discord: {
    token: string;
    prefix: string;
  };
  steam: {
    apiKey: string;
    rustAppId: string;
  };
  battlemetrics: {
    orgId: string;
    apiUrl: string;
    apiToken?: string;
    serverIds?: string[];
  };
  settings: {
    killDeathDays: number;
    cacheTimeMinutes: number;
    rateLimitMs: number;
  };
  api?: {
    port: number;
    mongoUrl: string;
  };
}

async function startAPI() {
  try {
    // Load configuration
    const configPath = join(process.cwd(), 'config.json');
    const config: Config = JSON.parse(readFileSync(configPath, 'utf-8'));

    // Use MongoDB URL from the main wipe countdown bot config if not specified
    const mongoUrl = config.api?.mongoUrl || '****************************************************';
    const port = config.api?.port || 3000;

    // Create API configuration
    const apiConfig = {
      port,
      mongoUrl,
      battlemetrics: config.battlemetrics,
      steam: config.steam,
      settings: config.settings
    };

    // Start the API server
    const api = new PlayerStatsAPI(apiConfig);
    await api.start(port);

  } catch (error) {
    console.error('Failed to start Player Stats API:', error);
    process.exit(1);
  }
}

// Start the API if this file is run directly
if (import.meta.main) {
  startAPI();
}

export { PlayerStatsAPI };
