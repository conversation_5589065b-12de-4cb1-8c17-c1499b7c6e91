import { Elysia, t } from 'elysia';
import { DatabaseService } from '../services/database.js';
import { PlayerStatsService } from '../services/playerStatsService.js';
import { BattlemetricsAPI } from '../services/battlemetrics.js';
import { SteamAPI } from '../services/steam.js';
import { DailyStatsService } from '../services/dailyStatsService.js';

interface ApiConfig {
  port: number;
  mongoUrl: string;
  battlemetrics: {
    orgId: string;
    apiUrl: string;
    apiToken?: string;
    serverIds?: string[];
    servers?: Array<{
      id: string;
      name: string;
    }>;
  };
  steam: {
    apiKey: string;
    rustAppId: string;
  };
  settings: {
    killDeathDays: number;
    cacheTimeMinutes: number;
    rateLimitMs: number;
  };
}

export class PlayerStatsAPI {
  private app: Elysia;
  private playerStatsService: PlayerStatsService;
  private dailyStatsService: DailyStatsService;
  private databaseService: DatabaseService;

  constructor(config: ApiConfig) {
    this.databaseService = DatabaseService.getInstance();

    // Initialize APIs
    const battlemetricsAPI = new BattlemetricsAPI(config.battlemetrics, config.settings);
    const steamAPI = new SteamAPI(config.steam, config.settings);
    this.playerStatsService = new PlayerStatsService(battlemetricsAPI, steamAPI);
    this.dailyStatsService = new DailyStatsService();

    this.app = new Elysia()
      .onStart(async () => {
        await this.databaseService.connect({
          mongoUrl: config.mongoUrl,
          dbName: 'rust_player_stats'
        });
        console.log(`Player Stats API starting on port ${config.port}`);
      })
      .onStop(async () => {
        await this.databaseService.disconnect();
        console.log('Player Stats API stopped');
      })
      .onError(({ error, code }) => {
        console.error(`API Error [${code}]:`, error);

        if (code === 'VALIDATION') {
          return {
            success: false,
            error: 'Invalid request data',
            details: error.message
          };
        }

        if (code === 'NOT_FOUND') {
          return {
            success: false,
            error: 'Resource not found'
          };
        }

        return {
          success: false,
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error'
        };
      })
      .get('/', () => ({
        message: 'Player Stats API',
        version: '1.0.0',
        endpoints: {
          'GET /api/player/:steamId': 'Get player stats by Steam ID',
          'POST /api/player': 'Create/update player stats by Steam ID',
          'PUT /api/player/:steamId': 'Update existing player stats',
          'DELETE /api/player/:steamId': 'Delete player stats',
          'GET /api/players': 'Get all players with pagination',
          'GET /api/search': 'Search players by name',
          'POST /api/daily/kill': 'Record a kill event',
          'POST /api/daily/death': 'Record a death event',
          'POST /api/daily/playtime': 'Record playtime session',
          'GET /api/daily/:steamId': 'Get daily stats for player',
          'GET /api/daily/:steamId/aggregated': 'Get aggregated stats for last N days'
        }
      }))
      .group('/api', (app) =>
        app
          // Get player stats by Steam ID (auto-create if not exists)
          .get('/player/:steamId', async ({ params: { steamId } }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            let playerStats = await this.playerStatsService.getPlayerStats(steamId);

            // If player doesn't exist in database, create it
            if (!playerStats) {
              console.log(`Player ${steamId} not found in database, creating new entry...`);
              try {
                playerStats = await this.playerStatsService.createOrUpdatePlayerStats({
                  steamId,
                  forceRefresh: false
                });
              } catch (error) {
                console.error(`Failed to create player stats for ${steamId}:`, error);
                throw new Error('Player not found on Battlemetrics or Steam');
              }
            }

            // Calculate total org hours
            const totalOrgHours = playerStats.orgServers.reduce((total, server) => total + server.hours, 0);

            return {
              success: true,
              data: {
                ...playerStats.toObject(),
                totalOrgHours // Add calculated total org hours
              }
            };
          }, {
            params: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 })
            })
          })

          // Create or update player stats
          .post('/player', async ({ body }) => {
            const { steamId, forceRefresh = false } = body;

            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.playerStatsService.createOrUpdatePlayerStats({
              steamId,
              forceRefresh
            });

            // Calculate total org hours
            const totalOrgHours = playerStats.orgServers.reduce((total, server) => total + server.hours, 0);

            return {
              success: true,
              data: {
                ...playerStats.toObject(),
                totalOrgHours // Add calculated total org hours
              },
              message: 'Player stats created/updated successfully'
            };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              forceRefresh: t.Optional(t.Boolean())
            })
          })

          // Update existing player stats
          .put('/player/:steamId', async ({ params: { steamId }, body }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.playerStatsService.updatePlayerStats(steamId, body);

            if (!playerStats) {
              throw new Error('Player not found');
            }

            return {
              success: true,
              data: playerStats,
              message: 'Player stats updated successfully'
            };
          }, {
            params: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 })
            }),
            body: t.Object({
              playerName: t.Optional(t.String()),
              totalHours: t.Optional(t.Number({ minimum: 0 })),
              kills: t.Optional(t.Number({ minimum: 0 })),
              deaths: t.Optional(t.Number({ minimum: 0 })),
              orgServers: t.Optional(t.Array(t.Object({
                id: t.String(),
                name: t.String(),
                hours: t.Number({ minimum: 0 })
              })))
            })
          })

          // Delete player stats
          .delete('/player/:steamId', async ({ params: { steamId } }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const deleted = await this.playerStatsService.deletePlayerStats(steamId);

            if (!deleted) {
              throw new Error('Player not found');
            }

            return {
              success: true,
              message: 'Player stats deleted successfully'
            };
          }, {
            params: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 })
            })
          })

          // Get all players with pagination
          .get('/players', async ({ query }) => {
            const page = Math.max(1, parseInt(query.page || '1'));
            const limit = Math.min(100, Math.max(1, parseInt(query.limit || '20')));

            const result = await this.playerStatsService.getPlayerStatsWithPagination(page, limit);

            return {
              success: true,
              data: result.players,
              pagination: result.pagination
            };
          }, {
            query: t.Object({
              page: t.Optional(t.String()),
              limit: t.Optional(t.String())
            })
          })

          // Search players by name
          .get('/search', async ({ query }) => {
            const { name, limit = '10' } = query;

            if (!name || name.trim().length < 2) {
              throw new Error('Search name must be at least 2 characters long');
            }

            const limitNum = Math.min(50, Math.max(1, parseInt(limit)));
            const players = await this.playerStatsService.searchPlayersByName(name.trim(), limitNum);

            return {
              success: true,
              data: players,
              count: players.length
            };
          }, {
            query: t.Object({
              name: t.String({ minLength: 2 }),
              limit: t.Optional(t.String())
            })
          })

          // Daily Stats Endpoints

          // Record a kill event
          .post('/daily/kill', async ({ body }) => {
            const { steamId, activity } = body;

            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.dailyStatsService.recordKill(
              steamId,
              activity ? {
                ...activity,
                data: activity.data || {}
              } : undefined
            );

            return {
              success: true,
              data: playerStats,
              message: 'Kill recorded successfully'
            };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              activity: t.Optional(t.Object({
                id: t.String(),
                type: t.String(),
                timestamp: t.String(),
                data: t.Optional(t.Object({
                  killer: t.Optional(t.Object({
                    name: t.String(),
                    id: t.String()
                  })),
                  victim: t.Optional(t.Object({
                    name: t.String(),
                    id: t.String()
                  })),
                  weapon: t.Optional(t.String()),
                  distance: t.Optional(t.Number())
                }))
              }))
            })
          })

          // Record a death event
          .post('/daily/death', async ({ body }) => {
            const { steamId, activity } = body;

            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.dailyStatsService.recordDeath(
              steamId,
              activity ? {
                ...activity,
                data: activity.data || {}
              } : undefined
            );

            return {
              success: true,
              data: playerStats,
              message: 'Death recorded successfully'
            };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              activity: t.Optional(t.Object({
                id: t.String(),
                type: t.String(),
                timestamp: t.String(),
                data: t.Optional(t.Object({
                  killer: t.Optional(t.Object({
                    name: t.String(),
                    id: t.String()
                  })),
                  victim: t.Optional(t.Object({
                    name: t.String(),
                    id: t.String()
                  })),
                  weapon: t.Optional(t.String()),
                  distance: t.Optional(t.Number())
                }))
              }))
            })
          })

          // Record playtime session
          .post('/daily/playtime', async ({ body }) => {
            const { steamId, serverId, serverName, minutes } = body;

            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.dailyStatsService.recordPlaytime(
              steamId,
              serverId,
              serverName,
              minutes
            );

            return {
              success: true,
              data: playerStats,
              message: 'Playtime recorded successfully'
            };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              serverId: t.String(),
              serverName: t.String(),
              minutes: t.Number({ minimum: 0 })
            })
          })

          // Get daily stats for a player
          .get('/daily/:steamId', async ({ params: { steamId }, query }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const { startDate, endDate } = query;
            const dailyStats = await this.dailyStatsService.getDailyStats(steamId, startDate, endDate);

            return {
              success: true,
              data: dailyStats,
              count: dailyStats.length
            };
          }, {
            params: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 })
            }),
            query: t.Object({
              startDate: t.Optional(t.String()),
              endDate: t.Optional(t.String())
            })
          })

          // Get aggregated stats for last N days
          .get('/daily/:steamId/aggregated', async ({ params: { steamId }, query }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const days = Math.min(30, Math.max(1, parseInt(query.days || '14')));
            const aggregatedStats = await this.dailyStatsService.getAggregatedStats(steamId, days);

            return {
              success: true,
              data: aggregatedStats,
              period: `Last ${days} days`
            };
          }, {
            params: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 })
            }),
            query: t.Object({
              days: t.Optional(t.String())
            })
          })
      );
  }

  async start(port: number): Promise<void> {
    this.app.listen(port);
    console.log(`Player Stats API is running on http://localhost:${port}`);
  }

  getApp(): Elysia {
    return this.app;
  }
}
