import { Elysia, t } from 'elysia';
import { DatabaseService } from '../services/database.js';
import { PlayerStatsService } from '../services/playerStatsService.js';
import { BattlemetricsAPI } from '../services/battlemetrics.js';
import { SteamAPI } from '../services/steam.js';

interface ApiConfig {
  port: number;
  mongoUrl: string;
  battlemetrics: {
    orgId: string;
    apiUrl: string;
    apiToken?: string;
    serverIds?: string[];
    servers?: Array<{
      id: string;
      name: string;
    }>;
  };
  steam: {
    apiKey: string;
    rustAppId: string;
  };
  settings: {
    killDeathDays: number;
    cacheTimeMinutes: number;
    rateLimitMs: number;
  };
}

export class PlayerStatsAPI {
  private app: Elysia;
  private playerStatsService: PlayerStatsService;
  private databaseService: DatabaseService;

  constructor(config: ApiConfig) {
    this.databaseService = DatabaseService.getInstance();

    // Initialize APIs
    const battlemetricsAPI = new BattlemetricsAPI(config.battlemetrics, config.settings);
    const steamAPI = new SteamAPI(config.steam, config.settings);
    this.playerStatsService = new PlayerStatsService(battlemetricsAPI, steamAPI);

    this.app = new Elysia()
      .onStart(async () => {
        await this.databaseService.connect({
          mongoUrl: config.mongoUrl,
          dbName: 'rust_player_stats'
        });
        console.log(`Player Stats API starting on port ${config.port}`);
      })
      .onStop(async () => {
        await this.databaseService.disconnect();
        console.log('Player Stats API stopped');
      })
      .onError(({ error, code }) => {
        console.error(`API Error [${code}]:`, error);

        if (code === 'VALIDATION') {
          return {
            success: false,
            error: 'Invalid request data',
            details: error.message
          };
        }

        if (code === 'NOT_FOUND') {
          return {
            success: false,
            error: 'Resource not found'
          };
        }

        return {
          success: false,
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error'
        };
      })
      .get('/', () => ({
        message: 'Player Stats API',
        version: '1.0.0',
        endpoints: {
          'GET /api/player/:steamId': 'Get player stats by Steam ID',
          'POST /api/player': 'Create/update player stats by Steam ID',
          'PUT /api/player/:steamId': 'Update existing player stats',
          'DELETE /api/player/:steamId': 'Delete player stats',
          'GET /api/players': 'Get all players with pagination',
          'GET /api/search': 'Search players by name'
        }
      }))
      .group('/api', (app) =>
        app
          // Get player stats by Steam ID
          .get('/player/:steamId', async ({ params: { steamId } }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.playerStatsService.getPlayerStats(steamId);

            if (!playerStats) {
              throw new Error('Player not found');
            }

            return {
              success: true,
              data: playerStats
            };
          }, {
            params: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 })
            })
          })

          // Create or update player stats
          .post('/player', async ({ body }) => {
            const { steamId, forceRefresh = false } = body;

            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.playerStatsService.createOrUpdatePlayerStats({
              steamId,
              forceRefresh
            });

            return {
              success: true,
              data: playerStats,
              message: 'Player stats created/updated successfully'
            };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              forceRefresh: t.Optional(t.Boolean())
            })
          })

          // Update existing player stats
          .put('/player/:steamId', async ({ params: { steamId }, body }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.playerStatsService.updatePlayerStats(steamId, body);

            if (!playerStats) {
              throw new Error('Player not found');
            }

            return {
              success: true,
              data: playerStats,
              message: 'Player stats updated successfully'
            };
          }, {
            params: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 })
            }),
            body: t.Object({
              playerName: t.Optional(t.String()),
              totalHours: t.Optional(t.Number({ minimum: 0 })),
              kills: t.Optional(t.Number({ minimum: 0 })),
              deaths: t.Optional(t.Number({ minimum: 0 })),
              orgServers: t.Optional(t.Array(t.Object({
                id: t.String(),
                name: t.String(),
                hours: t.Number({ minimum: 0 })
              })))
            })
          })

          // Delete player stats
          .delete('/player/:steamId', async ({ params: { steamId } }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const deleted = await this.playerStatsService.deletePlayerStats(steamId);

            if (!deleted) {
              throw new Error('Player not found');
            }

            return {
              success: true,
              message: 'Player stats deleted successfully'
            };
          }, {
            params: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 })
            })
          })

          // Get all players with pagination
          .get('/players', async ({ query }) => {
            const page = Math.max(1, parseInt(query.page || '1'));
            const limit = Math.min(100, Math.max(1, parseInt(query.limit || '20')));

            const result = await this.playerStatsService.getPlayerStatsWithPagination(page, limit);

            return {
              success: true,
              data: result.players,
              pagination: result.pagination
            };
          }, {
            query: t.Object({
              page: t.Optional(t.String()),
              limit: t.Optional(t.String())
            })
          })

          // Search players by name
          .get('/search', async ({ query }) => {
            const { name, limit = '10' } = query;

            if (!name || name.trim().length < 2) {
              throw new Error('Search name must be at least 2 characters long');
            }

            const limitNum = Math.min(50, Math.max(1, parseInt(limit)));
            const players = await this.playerStatsService.searchPlayersByName(name.trim(), limitNum);

            return {
              success: true,
              data: players,
              count: players.length
            };
          }, {
            query: t.Object({
              name: t.String({ minLength: 2 }),
              limit: t.Optional(t.String())
            })
          })
      );
  }

  async start(port: number): Promise<void> {
    this.app.listen(port);
    console.log(`Player Stats API is running on http://localhost:${port}`);
  }

  getApp(): Elysia {
    return this.app;
  }
}
