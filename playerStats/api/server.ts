import { Elysia, t } from 'elysia';
import { PlayerStatsService } from '../services/playerStatsService.ts';
import { BattlemetricsAPI } from '../services/battlemetrics.ts';
import { SteamAPI } from '../services/steam.ts';
import { IPlayerStats, IActivityLogEntry, IDailyStats } from '../models/PlayerStats.js';
import { DatabaseService, DailyStatsService } from '../services/database.js';

// API Configuration
interface ApiConfig {
  port: number;
  mongoUrl: string;
  battlemetrics: {
    orgId: string;
    apiUrl: string;
    apiToken?: string;
    serverIds?: string[];
    servers?: Array<{ id: string; name: string }>;
  };
  steam: {
    apiKey: string;
    rustAppId: string;
  };
  settings: {
    killDeathDays: number;
    cacheTimeMinutes: number;
    rateLimitMs: number;
  };
}

// Main API
export class PlayerStatsAPI {
  private app: Elysia;
  private playerStatsService: PlayerStatsService;
  private dailyStatsService: DailyStatsService;

  constructor(config: ApiConfig) {
    this.dailyStatsService = new DailyStatsService();
    const battlemetricsAPI = new BattlemetricsAPI(config.battlemetrics, config.settings);
    const steamAPI = new SteamAPI(config.steam, config.settings);
    this.playerStatsService = new PlayerStatsService(battlemetricsAPI, steamAPI);

    this.app = new Elysia()
      .onStart(async () => {
        const dbService = DatabaseService.getInstance();
        await dbService.connect({ mongoUrl: config.mongoUrl, dbName: 'rust_player_stats' });
        console.log(`Player Stats API starting on port ${config.port}`);
      })
      .onStop(async () => {
        const dbService = DatabaseService.getInstance();
        await dbService.disconnect();
        console.log('Player Stats API stopped');
      })
      .onError(({ error, code }) => {
        console.error(`API Error [${code}]:`, error);
        if (code === 'VALIDATION') {
          return { success: false, error: 'Invalid request data', details: error.message };
        }
        if (code === 'NOT_FOUND') {
          return { success: false, error: 'Resource not found' };
        }
        return {
          success: false,
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error'
        };
      })
      .get('/', () => ({
        message: 'Player Stats API',
        version: '1.0.0',
        endpoints: {
          'GET /api/player/:steamId': 'Get player stats by Steam ID',
          'POST /api/player': 'Create/update player stats by Steam ID',
          'PUT /api/player/:steamId': 'Update existing player stats',
          'DELETE /api/player/:steamId': 'Delete player stats',
          'GET /api/players': 'Get all players with pagination',
          'GET /api/search': 'Search players by name',
          'POST /api/daily/kill': 'Record a kill or headshot event',
          'POST /api/daily/death': 'Record a death event',
          'POST /api/daily/playtime': 'Record playtime session',
          'POST /api/daily/activity': 'Record non-kill activities (e.g., rocket_shot, structure_built)',
          'GET /api/daily/:steamId': 'Get daily stats for player',
          'GET /api/daily/:steamId/aggregated': 'Get aggregated stats for last N days'
        }
      }))
      .group('/api', (app) =>
        app
          .get('/player/:steamId', async ({ params: { steamId } }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            let playerStats = await this.playerStatsService.getPlayerStats(steamId);
            if (!playerStats) {
              console.log(`Player ${steamId} not found, creating new entry...`);
              try {
                playerStats = await this.playerStatsService.createOrUpdatePlayerStats({ steamId, forceRefresh: false });
              } catch (error) {
                console.error(`Failed to create player stats for ${steamId}:`, error);
                throw new Error('Player not found on Battlemetrics or Steam');
              }
            }

            const totalOrgHours = playerStats.orgServers.reduce((total, server) => total + server.hours, 0);
            return { success: true, data: { ...playerStats.toObject(), totalOrgHours } };
          }, {
            params: t.Object({ steamId: t.String({ minLength: 17, maxLength: 17 }) })
          })
          .post('/player', async ({ body }) => {
            const { steamId, forceRefresh = false } = body;
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.playerStatsService.createOrUpdatePlayerStats({ steamId, forceRefresh });
            const totalOrgHours = playerStats.orgServers.reduce((total, server) => total + server.hours, 0);
            return {
              success: true,
              data: { ...playerStats.toObject(), totalOrgHours },
              message: 'Player stats created/updated successfully'
            };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              forceRefresh: t.Optional(t.Boolean())
            })
          })
          .put('/player/:steamId', async ({ params: { steamId }, body }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.playerStatsService.updatePlayerStats(steamId, body);
            if (!playerStats) {
              throw new Error('Player not found');
            }
            return { success: true, data: playerStats, message: 'Player stats updated successfully' };
          }, {
            params: t.Object({ steamId: t.String({ minLength: 17, maxLength: 17 }) }),
            body: t.Object({
              playerName: t.Optional(t.String()),
              totalHours: t.Optional(t.Number({ minimum: 0 })),
              kills: t.Optional(t.Number({ minimum: 0 })),
              deaths: t.Optional(t.Number({ minimum: 0 })),
              rocketsShot: t.Optional(t.Number({ minimum: 0 })),
              c4Thrown: t.Optional(t.Number({ minimum: 0 })),
              headshots: t.Optional(t.Number({ minimum: 0 })),
              resourcesGathered: t.Optional(t.Record(t.String(), t.Number({ minimum: 0 }))),
              structuresBuilt: t.Optional(t.Number({ minimum: 0 })),
              itemsCrafted: t.Optional(t.Number({ minimum: 0 })),
              explosivesUsed: t.Optional(t.Number({ minimum: 0 })),
              animalsKilled: t.Optional(t.Number({ minimum: 0 })),
              barrelsLooted: t.Optional(t.Number({ minimum: 0 })),
              orgServers: t.Optional(t.Array(t.Object({
                id: t.String(),
                name: t.String(),
                hours: t.Number({ minimum: 0 })
              })))
            })
          })
          .delete('/player/:steamId', async ({ params: { steamId } }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const deleted = await this.playerStatsService.deletePlayerStats(steamId);
            if (!deleted) {
              throw new Error('Player not found');
            }
            return { success: true, message: 'Player stats deleted successfully' };
          }, {
            params: t.Object({ steamId: t.String({ minLength: 17, maxLength: 17 }) })
          })
          .get('/players', async ({ query }) => {
            const page = Math.max(1, parseInt(query.page || '1'));
            const limit = Math.min(100, Math.max(1, parseInt(query.limit || '20')));

            const result = await this.playerStatsService.getPlayerStatsWithPagination(page, limit);
            return { success: true, data: result.players, pagination: result.pagination };
          }, {
            query: t.Object({ page: t.Optional(t.String()), limit: t.Optional(t.String()) })
          })
          .get('/search', async ({ query }) => {
            const { name, limit = '10' } = query;
            if (!name || name.trim().length < 2) {
              throw new Error('Search name must be at least 2 characters long');
            }

            const limitNum = Math.min(50, Math.max(1, parseInt(limit)));
            const players = await this.playerStatsService.searchPlayersByName(name.trim(), limitNum);
            return { success: true, data: players, count: players.length };
          }, {
            query: t.Object({ name: t.String({ minLength: 2 }), limit: t.Optional(t.String()) })
          })
          .post('/daily/kill', async ({ body }) => {
            const { steamId, activity } = body;
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            if (activity && !['kill', 'headshot'].includes(activity.type)) {
              throw new Error('Invalid activity type for /daily/kill. Use /daily/activity for other types.');
            }

            const playerStats = await this.dailyStatsService.recordKill(
              steamId,
              activity ? {
                ...activity,
                data: {
                  killer: activity.data?.killer || undefined,
                  victim: activity.data?.victim || undefined,
                  weapon: activity.data?.weapon || undefined,
                  distance: activity.data?.distance || undefined
                }
              } : undefined
            );
            return { success: true, data: playerStats, message: 'Kill recorded successfully' };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              activity: t.Optional(t.Object({
                id: t.String(),
                type: t.String({ enum: ['kill', 'headshot'] }),
                timestamp: t.String(),
                data: t.Optional(t.Object({
                  killer: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  victim: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  weapon: t.Optional(t.Nullable(t.String())),
                  distance: t.Optional(t.Nullable(t.Number()))
                }))
              }))
            })
          })
          .post('/daily/death', async ({ body }) => {
            const { steamId, activity } = body;
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.dailyStatsService.recordDeath(
              steamId,
              activity ? {
                ...activity,
                data: {
                  killer: activity.data?.killer || undefined,
                  victim: activity.data?.victim || undefined,
                  weapon: activity.data?.weapon || undefined,
                  distance: activity.data?.distance || undefined
                }
              } : undefined
            );
            return { success: true, data: playerStats, message: 'Death recorded successfully' };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              activity: t.Optional(t.Object({
                id: t.String(),
                type: t.String(),
                timestamp: t.String(),
                data: t.Optional(t.Object({
                  killer: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  victim: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  weapon: t.Optional(t.Nullable(t.String())),
                  distance: t.Optional(t.Nullable(t.Number()))
                }))
              }))
            })
          })
          .post('/daily/playtime', async ({ body }) => {
            const { steamId, serverId, serverName, minutes } = body;
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.dailyStatsService.recordPlaytime(steamId, serverId, serverName, minutes);
            return { success: true, data: playerStats, message: 'Playtime recorded successfully' };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              serverId: t.String(),
              serverName: t.String(),
              minutes: t.Number({ minimum: 0 })
            })
          })
          .post('/daily/activity', async ({ body }) => {
            const { steamId, activity } = body;
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const validActivityTypes = [
              'rocket_shot', 'c4_thrown', 'satchel_thrown', 'explosive_thrown',
              'resource_gathered', 'structure_built', 'item_crafted',
              'animal_killed', 'barrel_looted'
            ];

            if (!validActivityTypes.includes(activity.type)) {
              throw new Error(`Invalid activity type. Must be one of: ${validActivityTypes.join(', ')}`);
            }

            const playerStats = await this.dailyStatsService.recordActivity(steamId, {
              ...activity,
              data: {
                killer: activity.data?.killer || undefined,
                victim: activity.data?.victim || undefined,
                weapon: activity.data?.weapon || undefined,
                distance: activity.data?.distance || undefined,
                resourceType: activity.data?.resourceType || undefined,
                amount: activity.data?.amount || undefined
              }
            });
            return { success: true, data: playerStats, message: 'Activity recorded successfully' };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              activity: t.Object({
                id: t.String(),
                type: t.String({
                  enum: [
                    'rocket_shot', 'c4_thrown', 'satchel_thrown', 'explosive_thrown',
                    'resource_gathered', 'structure_built', 'item_crafted',
                    'animal_killed', 'barrel_looted'
                  ]
                }),
                timestamp: t.String(),
                data: t.Optional(t.Object({
                  killer: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  victim: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  weapon: t.Optional(t.Nullable(t.String())),
                  distance: t.Optional(t.Nullable(t.Number())),
                  resourceType: t.Optional(t.Nullable(t.String())),
                  amount: t.Optional(t.Nullable(t.Number()))
                }))
              })
            })
          })
          .get('/daily/:steamId', async ({ params: { steamId }, query }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const { startDate, endDate } = query;
            const dailyStats = await this.dailyStatsService.getDailyStats(steamId, startDate, endDate);
            return { success: true, data: dailyStats, count: dailyStats.length };
          }, {
            params: t.Object({ steamId: t.String({ minLength: 17, maxLength: 17 }) }),
            query: t.Object({ startDate: t.Optional(t.String()), endDate: t.Optional(t.String()) })
          })
          .get('/daily/:steamId/aggregated', async ({ params: { steamId }, query }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const days = Math.min(30, Math.max(1, parseInt(query.days || '14')));
            const aggregatedStats = await this.dailyStatsService.getAggregatedStats(steamId, days);
            return { success: true, data: aggregatedStats, period: `Last ${days} days` };
          }, {
            params: t.Object({ steamId: t.String({ minLength: 17, maxLength: 17 }) }),
            query: t.Object({ days: t.Optional(t.String()) })
          })
      );
  }

  async start(port: number): Promise<void> {
    this.app.listen(port);
    console.log(`Player Stats API is running on http://localhost:${port}`);
  }

  getApp(): Elysia {
    return this.app;
  }
}