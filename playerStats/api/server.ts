import { Elysia, t } from 'elysia';
import mongoose, { Schema, Document, connect, disconnect } from 'mongoose';
import { PlayerStatsService } from '../services/playerStatsService.ts';
import { BattlemetricsAPI } from '../services/battlemetrics.ts';
import { SteamAPI } from '../services/steam.ts';

// MongoDB Model
interface IServerInfo {
  id: string;
  name: string;
  hours: number;
}

interface IActivityLogEntry {
  id: string;
  type: string;
  timestamp: string;
  data: {
    killer?: { name: string; id: string } | null;
    victim?: { name: string; id: string } | null;
    weapon?: string | null;
    distance?: number | null;
    resourceType?: string | null;
    amount?: number | null;
  };
}

interface IDailyStats {
  date: string;
  kills: number;
  deaths: number;
  rocketsShot: number;
  c4Thrown: number;
  headshots: number;
  resourcesGathered: Record<string, number>;
  structuresBuilt: number;
  itemsCrafted: number;
  explosivesUsed: number;
  animalsKilled: number;
  barrelsLooted: number;
  playtimeMinutes: number;
  serverSessions: Array<{ serverId: string; serverName: string; minutes: number }>;
  activities: IActivityLogEntry[];
}

interface IPlayerStats extends Document {
  steamId: string;
  playerId: string;
  playerName: string;
  totalHours: number;
  orgServers: IServerInfo[];
  kills: number;
  deaths: number;
  killDeathRatio: number;
  rocketsShot: number;
  c4Thrown: number;
  headshots: number;
  resourcesGathered: Record<string, number>;
  structuresBuilt: number;
  itemsCrafted: number;
  explosivesUsed: number;
  animalsKilled: number;
  barrelsLooted: number;
  recentActivity: IActivityLogEntry[];
  dailyStats: IDailyStats[];
  lastUpdated: Date;
  createdAt: Date;
}

const ServerInfoSchema = new Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  hours: { type: Number, required: true, default: 0 }
}, { _id: false });

const ActivityLogEntrySchema = new Schema({
  id: { type: String, required: true },
  type: { type: String, required: true },
  timestamp: { type: String, required: true },
  data: {
    killer: { name: String, id: String },
    victim: { name: String, id: String },
    weapon: String,
    distance: Number,
    resourceType: String,
    amount: Number
  }
}, { _id: false });

const DailyStatsSchema = new Schema({
  date: { type: String, required: true },
  kills: { type: Number, required: true, default: 0 },
  deaths: { type: Number, required: true, default: 0 },
  rocketsShot: { type: Number, required: true, default: 0 },
  c4Thrown: { type: Number, required: true, default: 0 },
  headshots: { type: Number, required: true, default: 0 },
  resourcesGathered: { type: Map, of: Number, default: {} },
  structuresBuilt: { type: Number, required: true, default: 0 },
  itemsCrafted: { type: Number, required: true, default: 0 },
  explosivesUsed: { type: Number, required: true, default: 0 },
  animalsKilled: { type: Number, required: true, default: 0 },
  barrelsLooted: { type: Number, required: true, default: 0 },
  playtimeMinutes: { type: Number, required: true, default: 0 },
  serverSessions: [{
    serverId: { type: String, required: true },
    serverName: { type: String, required: true },
    minutes: { type: Number, required: true, default: 0 }
  }],
  activities: [ActivityLogEntrySchema]
}, { _id: false });

const PlayerStatsSchema = new Schema({
  steamId: { type: String, required: true, unique: true },
  playerId: { type: String, required: true },
  playerName: { type: String, required: true },
  totalHours: { type: Number, required: true, default: 0 },
  orgServers: [ServerInfoSchema],
  kills: { type: Number, required: true, default: 0 },
  deaths: { type: Number, required: true, default: 0 },
  killDeathRatio: { type: Number, required: true, default: 0 },
  rocketsShot: { type: Number, required: true, default: 0 },
  c4Thrown: { type: Number, required: true, default: 0 },
  headshots: { type: Number, required: true, default: 0 },
  resourcesGathered: { type: Map, of: Number, default: {} },
  structuresBuilt: { type: Number, required: true, default: 0 },
  itemsCrafted: { type: Number, required: true, default: 0 },
  explosivesUsed: { type: Number, required: true, default: 0 },
  animalsKilled: { type: Number, required: true, default: 0 },
  barrelsLooted: { type: Number, required: true, default: 0 },
  recentActivity: [ActivityLogEntrySchema],
  dailyStats: [DailyStatsSchema],
  lastUpdated: { type: Date, default: Date.now }
}, { timestamps: true });

PlayerStatsSchema.pre('save', function(next) {
  const fourteenDaysAgo = new Date();
  fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);
  const cutoffDate = fourteenDaysAgo.toISOString().split('T')[0];

  this.dailyStats = this.dailyStats.filter(dayStats => dayStats.date >= cutoffDate);

  this.kills = this.dailyStats.reduce((total, day) => total + day.kills, 0);
  this.deaths = this.dailyStats.reduce((total, day) => total + day.deaths, 0);
  this.rocketsShot = this.dailyStats.reduce((total, day) => total + day.rocketsShot, 0);
  this.c4Thrown = this.dailyStats.reduce((total, day) => total + day.c4Thrown, 0);
  this.headshots = this.dailyStats.reduce((total, day) => total + day.headshots, 0);
  this.resourcesGathered = this.dailyStats.reduce((acc, day) => {
    for (const [type, amount] of Object.entries(day.resourcesGathered)) {
      acc[type] = (acc[type] || 0) + amount;
    }
    return acc;
  }, {});
  this.structuresBuilt = this.dailyStats.reduce((total, day) => total + day.structuresBuilt, 0);
  this.itemsCrafted = this.dailyStats.reduce((total, day) => total + day.itemsCrafted, 0);
  this.explosivesUsed = this.dailyStats.reduce((total, day) => total + day.explosivesUsed, 0);
  this.animalsKilled = this.dailyStats.reduce((total, day) => total + day.animalsKilled, 0);
  this.barrelsLooted = this.dailyStats.reduce((total, day) => total + day.barrelsLooted, 0);

  if (this.deaths > 0) {
    this.killDeathRatio = Number((this.kills / this.deaths).toFixed(2));
  } else {
    this.killDeathRatio = this.kills;
  }

  this.recentActivity = this.dailyStats
    .flatMap(day => day.activities)
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 50);

  this.lastUpdated = new Date();
  next();
});

PlayerStatsSchema.index({ steamId: 1 });
PlayerStatsSchema.index({ playerName: 1 });
PlayerStatsSchema.index({ lastUpdated: -1 });

const PlayerStats = mongoose.model<IPlayerStats>('PlayerStats', PlayerStatsSchema);

// Daily Stats Service Logic
class DailyStatsService {
  async recordActivity(steamId: string, activity: IActivityLogEntry): Promise<IPlayerStats> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const validActivityTypes = [
      'rocket_shot', 'c4_thrown', 'satchel_thrown', 'explosive_thrown',
      'resource_gathered', 'structure_built', 'item_crafted',
      'animal_killed', 'barrel_looted'
    ];

    if (!validActivityTypes.includes(activity.type)) {
      throw new Error(`Invalid activity type: ${activity.type}`);
    }

    const date = new Date(activity.timestamp).toISOString().split('T')[0];
    const update: any = {
      $set: { lastUpdated: new Date() },
      $push: { recentActivity: activity }
    };

    update.$setOnInsert = {
      steamId,
      playerId: steamId,
      playerName: 'Unknown',
      totalHours: 0,
      kills: 0,
      deaths: 0,
      killDeathRatio: 0,
      rocketsShot: 0,
      c4Thrown: 0,
      headshots: 0,
      resourcesGathered: {},
      structuresBuilt: 0,
      itemsCrafted: 0,
      explosivesUsed: 0,
      animalsKilled: 0,
      barrelsLooted: 0,
      orgServers: [],
      dailyStats: []
    };

    const dailyStatsUpdate: any = {
      'dailyStats.$.date': date,
      'dailyStats.$.activities': { $each: [activity] }
    };

    switch (activity.type) {
      case 'rocket_shot':
        dailyStatsUpdate['dailyStats.$.rocketsShot'] = { $inc: 1 };
        break;
      case 'c4_thrown':
        dailyStatsUpdate['dailyStats.$.c4Thrown'] = { $inc: 1 };
        dailyStatsUpdate['dailyStats.$.explosivesUsed'] = { $inc: 1 };
        break;
      case 'satchel_thrown':
      case 'explosive_thrown':
        dailyStatsUpdate['dailyStats.$.explosivesUsed'] = { $inc: 1 };
        break;
      case 'resource_gathered':
        if (activity.data.resourceType && activity.data.amount) {
          dailyStatsUpdate[`dailyStats.$.resourcesGathered.${activity.data.resourceType}`] = { $inc: activity.data.amount };
        }
        break;
      case 'structure_built':
        dailyStatsUpdate['dailyStats.$.structuresBuilt'] = { $inc: 1 };
        break;
      case 'item_crafted':
        dailyStatsUpdate['dailyStats.$.itemsCrafted'] = { $inc: 1 };
        break;
      case 'animal_killed':
        dailyStatsUpdate['dailyStats.$.animalsKilled'] = { $inc: 1 };
        break;
      case 'barrel_looted':
        dailyStatsUpdate['dailyStats.$.barrelsLooted'] = { $inc: 1 };
        break;
    }

    let player = await PlayerStats.findOneAndUpdate(
      { steamId, 'dailyStats.date': date },
      {
        $push: { 'dailyStats.$.activities': activity },
        $inc: dailyStatsUpdate
      },
      { new: true }
    );

    if (!player) {
      const newDailyStats: IDailyStats = {
        date,
        kills: 0,
        deaths: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: 0,
        serverSessions: [],
        activities: [activity]
      };

      switch (activity.type) {
        case 'rocket_shot':
          newDailyStats.rocketsShot = 1;
          break;
        case 'c4_thrown':
          newDailyStats.c4Thrown = 1;
          newDailyStats.explosivesUsed = 1;
          break;
        case 'satchel_thrown':
        case 'explosive_thrown':
          newDailyStats.explosivesUsed = 1;
          break;
        case 'resource_gathered':
          if (activity.data.resourceType && activity.data.amount) {
            newDailyStats.resourcesGathered[activity.data.resourceType] = activity.data.amount;
          }
          break;
        case 'structure_built':
          newDailyStats.structuresBuilt = 1;
          break;
        case 'item_crafted':
          newDailyStats.itemsCrafted = 1;
          break;
        case 'animal_killed':
          newDailyStats.animalsKilled = 1;
          break;
        case 'barrel_looted':
          newDailyStats.barrelsLooted = 1;
          break;
      }

      player = await PlayerStats.findOneAndUpdate(
        { steamId },
        {
          ...update,
          $push: { dailyStats: newDailyStats }
        },
        { upsert: true, new: true }
      );
    }

    return player;
  }

  async recordKill(steamId: string, activity?: IActivityLogEntry): Promise<IPlayerStats> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const date = activity ? new Date(activity.timestamp).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
    const update: any = {
      $set: { lastUpdated: new Date() }
    };

    if (activity) {
      update.$push = { recentActivity: activity };
    }

    update.$setOnInsert = {
      steamId,
      playerId: steamId,
      playerName: 'Unknown',
      totalHours: 0,
      kills: 0,
      deaths: 0,
      killDeathRatio: 0,
      rocketsShot: 0,
      c4Thrown: 0,
      headshots: 0,
      resourcesGathered: {},
      structuresBuilt: 0,
      itemsCrafted: 0,
      explosivesUsed: 0,
      animalsKilled: 0,
      barrelsLooted: 0,
      orgServers: [],
      dailyStats: []
    };

    let player = await PlayerStats.findOneAndUpdate(
      { steamId, 'dailyStats.date': date },
      {
        $inc: {
          'dailyStats.$.kills': 1,
          'dailyStats.$.headshots': activity?.type === 'headshot' ? 1 : 0
        },
        ...(activity && { $push: { 'dailyStats.$.activities': activity } })
      },
      { new: true }
    );

    if (!player) {
      const newDailyStats: IDailyStats = {
        date,
        kills: 1,
        deaths: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: activity?.type === 'headshot' ? 1 : 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: 0,
        serverSessions: [],
        activities: activity ? [activity] : []
      };

      player = await PlayerStats.findOneAndUpdate(
        { steamId },
        {
          ...update,
          $push: { dailyStats: newDailyStats }
        },
        { upsert: true, new: true }
      );
    }

    return player;
  }

  async recordDeath(steamId: string, activity?: IActivityLogEntry): Promise<IPlayerStats> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const date = activity ? new Date(activity.timestamp).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
    const update: any = {
      $set: { lastUpdated: new Date() }
    };

    if (activity) {
      update.$push = { recentActivity: activity };
    }

    update.$setOnInsert = {
      steamId,
      playerId: steamId,
      playerName: 'Unknown',
      totalHours: 0,
      kills: 0,
      deaths: 0,
      killDeathRatio: 0,
      rocketsShot: 0,
      c4Thrown: 0,
      headshots: 0,
      resourcesGathered: {},
      structuresBuilt: 0,
      itemsCrafted: 0,
      explosivesUsed: 0,
      animalsKilled: 0,
      barrelsLooted: 0,
      orgServers: [],
      dailyStats: []
    };

    let player = await PlayerStats.findOneAndUpdate(
      { steamId, 'dailyStats.date': date },
      {
        $inc: { 'dailyStats.$.deaths': 1 },
        ...(activity && { $push: { 'dailyStats.$.activities': activity } })
      },
      { new: true }
    );

    if (!player) {
      const newDailyStats: IDailyStats = {
        date,
        kills: 0,
        deaths: 1,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: 0,
        serverSessions: [],
        activities: activity ? [activity] : []
      };

      player = await PlayerStats.findOneAndUpdate(
        { steamId },
        {
          ...update,
          $push: { dailyStats: newDailyStats }
        },
        { upsert: true, new: true }
      );
    }

    return player;
  }

  async recordPlaytime(steamId: string, serverId: string, serverName: string, minutes: number): Promise<IPlayerStats> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const date = new Date().toISOString().split('T')[0];
    const update: any = {
      $set: { lastUpdated: new Date() },
      $setOnInsert: {
        steamId,
        playerId: steamId,
        playerName: 'Unknown',
        totalHours: 0,
        kills: 0,
        deaths: 0,
        killDeathRatio: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        orgServers: [],
        dailyStats: []
      }
    };

    let player = await PlayerStats.findOneAndUpdate(
      { steamId, 'dailyStats.date': date },
      {
        $inc: { 'dailyStats.$.playtimeMinutes': minutes },
        $push: {
          'dailyStats.$.serverSessions': { serverId, serverName, minutes }
        }
      },
      { new: true }
    );

    if (!player) {
      const newDailyStats: IDailyStats = {
        date,
        kills: 0,
        deaths: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: minutes,
        serverSessions: [{ serverId, serverName, minutes }],
        activities: []
      };

      player = await PlayerStats.findOneAndUpdate(
        { steamId },
        {
          ...update,
          $push: { dailyStats: newDailyStats }
        },
        { upsert: true, new: true }
      );
    }

    return player;
  }

  async getDailyStats(steamId: string, startDate?: string, endDate?: string): Promise<IDailyStats[]> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const query: any = { steamId };
    if (startDate || endDate) {
      query['dailyStats.date'] = {};
      if (startDate) query['dailyStats.date'].$gte = startDate;
      if (endDate) query['dailyStats.date'].$lte = endDate;
    }

    const player = await PlayerStats.findOne(query, { dailyStats: 1 });
    return player ? player.dailyStats : [];
  }

  async getAggregatedStats(steamId: string, days: number): Promise<Partial<IDailyStats>> {
    if (!/^\d{17}$/.test(steamId)) {
      throw new Error('Invalid Steam ID format. Must be 17 digits.');
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    const cutoffDateStr = cutoffDate.toISOString().split('T')[0];

    const player = await PlayerStats.findOne(
      { steamId, 'dailyStats.date': { $gte: cutoffDateStr } },
      { dailyStats: 1 }
    );

    if (!player) {
      return {
        kills: 0,
        deaths: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: 0,
        serverSessions: []
      };
    }

    return player.dailyStats.reduce(
      (acc, day) => ({
        kills: acc.kills + day.kills,
        deaths: acc.deaths + day.deaths,
        rocketsShot: acc.rocketsShot + day.rocketsShot,
        c4Thrown: acc.c4Thrown + day.c4Thrown,
        headshots: acc.headshots + day.headshots,
        resourcesGathered: Object.entries(day.resourcesGathered).reduce((res, [type, amount]) => {
          res[type] = (res[type] || 0) + amount;
          return res;
        }, acc.resourcesGathered),
        structuresBuilt: acc.structuresBuilt + day.structuresBuilt,
        itemsCrafted: acc.itemsCrafted + day.itemsCrafted,
        explosivesUsed: acc.explosivesUsed + day.explosivesUsed,
        animalsKilled: acc.animalsKilled + day.animalsKilled,
        barrelsLooted: acc.barrelsLooted + day.barrelsLooted,
        playtimeMinutes: acc.playtimeMinutes + day.playtimeMinutes,
        serverSessions: [...acc.serverSessions, ...day.serverSessions]
      }),
      {
        kills: 0,
        deaths: 0,
        rocketsShot: 0,
        c4Thrown: 0,
        headshots: 0,
        resourcesGathered: {},
        structuresBuilt: 0,
        itemsCrafted: 0,
        explosivesUsed: 0,
        animalsKilled: 0,
        barrelsLooted: 0,
        playtimeMinutes: 0,
        serverSessions: []
      }
    );
  }
}

// API Configuration
interface ApiConfig {
  port: number;
  mongoUrl: string;
  battlemetrics: {
    orgId: string;
    apiUrl: string;
    apiToken?: string;
    serverIds?: string[];
    servers?: Array<{ id: string; name: string }>;
  };
  steam: {
    apiKey: string;
    rustAppId: string;
  };
  settings: {
    killDeathDays: number;
    cacheTimeMinutes: number;
    rateLimitMs: number;
  };
}

// Main API
export class PlayerStatsAPI {
  private app: Elysia;
  private playerStatsService: PlayerStatsService;
  private dailyStatsService: DailyStatsService;

  constructor(config: ApiConfig) {
    this.dailyStatsService = new DailyStatsService();
    const battlemetricsAPI = new BattlemetricsAPI(config.battlemetrics, config.settings);
    const steamAPI = new SteamAPI(config.steam, config.settings);
    this.playerStatsService = new PlayerStatsService(battlemetricsAPI, steamAPI);

    this.app = new Elysia()
      .onStart(async () => {
        await connect(config.mongoUrl, { dbName: 'rust_player_stats' });
        console.log(`Player Stats API starting on port ${config.port}`);
      })
      .onStop(async () => {
        await disconnect();
        console.log('Player Stats API stopped');
      })
      .onError(({ error, code }) => {
        console.error(`API Error [${code}]:`, error);
        if (code === 'VALIDATION') {
          return { success: false, error: 'Invalid request data', details: error.message };
        }
        if (code === 'NOT_FOUND') {
          return { success: false, error: 'Resource not found' };
        }
        return {
          success: false,
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error'
        };
      })
      .get('/', () => ({
        message: 'Player Stats API',
        version: '1.0.0',
        endpoints: {
          'GET /api/player/:steamId': 'Get player stats by Steam ID',
          'POST /api/player': 'Create/update player stats by Steam ID',
          'PUT /api/player/:steamId': 'Update existing player stats',
          'DELETE /api/player/:steamId': 'Delete player stats',
          'GET /api/players': 'Get all players with pagination',
          'GET /api/search': 'Search players by name',
          'POST /api/daily/kill': 'Record a kill or headshot event',
          'POST /api/daily/death': 'Record a death event',
          'POST /api/daily/playtime': 'Record playtime session',
          'POST /api/daily/activity': 'Record non-kill activities (e.g., rocket_shot, structure_built)',
          'GET /api/daily/:steamId': 'Get daily stats for player',
          'GET /api/daily/:steamId/aggregated': 'Get aggregated stats for last N days'
        }
      }))
      .group('/api', (app) =>
        app
          .get('/player/:steamId', async ({ params: { steamId } }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            let playerStats = await this.playerStatsService.getPlayerStats(steamId);
            if (!playerStats) {
              console.log(`Player ${steamId} not found, creating new entry...`);
              try {
                playerStats = await this.playerStatsService.createOrUpdatePlayerStats({ steamId, forceRefresh: false });
              } catch (error) {
                console.error(`Failed to create player stats for ${steamId}:`, error);
                throw new Error('Player not found on Battlemetrics or Steam');
              }
            }

            const totalOrgHours = playerStats.orgServers.reduce((total, server) => total + server.hours, 0);
            return { success: true, data: { ...playerStats.toObject(), totalOrgHours } };
          }, {
            params: t.Object({ steamId: t.String({ minLength: 17, maxLength: 17 }) })
          })
          .post('/player', async ({ body }) => {
            const { steamId, forceRefresh = false } = body;
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.playerStatsService.createOrUpdatePlayerStats({ steamId, forceRefresh });
            const totalOrgHours = playerStats.orgServers.reduce((total, server) => total + server.hours, 0);
            return {
              success: true,
              data: { ...playerStats.toObject(), totalOrgHours },
              message: 'Player stats created/updated successfully'
            };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              forceRefresh: t.Optional(t.Boolean())
            })
          })
          .put('/player/:steamId', async ({ params: { steamId }, body }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.playerStatsService.updatePlayerStats(steamId, body);
            if (!playerStats) {
              throw new Error('Player not found');
            }
            return { success: true, data: playerStats, message: 'Player stats updated successfully' };
          }, {
            params: t.Object({ steamId: t.String({ minLength: 17, maxLength: 17 }) }),
            body: t.Object({
              playerName: t.Optional(t.String()),
              totalHours: t.Optional(t.Number({ minimum: 0 })),
              kills: t.Optional(t.Number({ minimum: 0 })),
              deaths: t.Optional(t.Number({ minimum: 0 })),
              rocketsShot: t.Optional(t.Number({ minimum: 0 })),
              c4Thrown: t.Optional(t.Number({ minimum: 0 })),
              headshots: t.Optional(t.Number({ minimum: 0 })),
              resourcesGathered: t.Optional(t.Record(t.String(), t.Number({ minimum: 0 }))),
              structuresBuilt: t.Optional(t.Number({ minimum: 0 })),
              itemsCrafted: t.Optional(t.Number({ minimum: 0 })),
              explosivesUsed: t.Optional(t.Number({ minimum: 0 })),
              animalsKilled: t.Optional(t.Number({ minimum: 0 })),
              barrelsLooted: t.Optional(t.Number({ minimum: 0 })),
              orgServers: t.Optional(t.Array(t.Object({
                id: t.String(),
                name: t.String(),
                hours: t.Number({ minimum: 0 })
              })))
            })
          })
          .delete('/player/:steamId', async ({ params: { steamId } }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const deleted = await this.playerStatsService.deletePlayerStats(steamId);
            if (!deleted) {
              throw new Error('Player not found');
            }
            return { success: true, message: 'Player stats deleted successfully' };
          }, {
            params: t.Object({ steamId: t.String({ minLength: 17, maxLength: 17 }) })
          })
          .get('/players', async ({ query }) => {
            const page = Math.max(1, parseInt(query.page || '1'));
            const limit = Math.min(100, Math.max(1, parseInt(query.limit || '20')));

            const result = await this.playerStatsService.getPlayerStatsWithPagination(page, limit);
            return { success: true, data: result.players, pagination: result.pagination };
          }, {
            query: t.Object({ page: t.Optional(t.String()), limit: t.Optional(t.String()) })
          })
          .get('/search', async ({ query }) => {
            const { name, limit = '10' } = query;
            if (!name || name.trim().length < 2) {
              throw new Error('Search name must be at least 2 characters long');
            }

            const limitNum = Math.min(50, Math.max(1, parseInt(limit)));
            const players = await this.playerStatsService.searchPlayersByName(name.trim(), limitNum);
            return { success: true, data: players, count: players.length };
          }, {
            query: t.Object({ name: t.String({ minLength: 2 }), limit: t.Optional(t.String()) })
          })
          .post('/daily/kill', async ({ body }) => {
            const { steamId, activity } = body;
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            if (activity && !['kill', 'headshot'].includes(activity.type)) {
              throw new Error('Invalid activity type for /daily/kill. Use /daily/activity for other types.');
            }

            const playerStats = await this.dailyStatsService.recordKill(
              steamId,
              activity ? { ...activity, data: activity.data || {} } : undefined
            );
            return { success: true, data: playerStats, message: 'Kill recorded successfully' };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              activity: t.Optional(t.Object({
                id: t.String(),
                type: t.String({ enum: ['kill', 'headshot'] }),
                timestamp: t.String(),
                data: t.Optional(t.Object({
                  killer: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  victim: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  weapon: t.Optional(t.Nullable(t.String())),
                  distance: t.Optional(t.Nullable(t.Number()))
                }))
              }))
            })
          })
          .post('/daily/death', async ({ body }) => {
            const { steamId, activity } = body;
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.dailyStatsService.recordDeath(
              steamId,
              activity ? { ...activity, data: activity.data || {} } : undefined
            );
            return { success: true, data: playerStats, message: 'Death recorded successfully' };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              activity: t.Optional(t.Object({
                id: t.String(),
                type: t.String(),
                timestamp: t.String(),
                data: t.Optional(t.Object({
                  killer: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  victim: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  weapon: t.Optional(t.Nullable(t.String())),
                  distance: t.Optional(t.Nullable(t.Number()))
                }))
              }))
            })
          })
          .post('/daily/playtime', async ({ body }) => {
            const { steamId, serverId, serverName, minutes } = body;
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const playerStats = await this.dailyStatsService.recordPlaytime(steamId, serverId, serverName, minutes);
            return { success: true, data: playerStats, message: 'Playtime recorded successfully' };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              serverId: t.String(),
              serverName: t.String(),
              minutes: t.Number({ minimum: 0 })
            })
          })
          .post('/daily/activity', async ({ body }) => {
            const { steamId, activity } = body;
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const validActivityTypes = [
              'rocket_shot', 'c4_thrown', 'satchel_thrown', 'explosive_thrown',
              'resource_gathered', 'structure_built', 'item_crafted',
              'animal_killed', 'barrel_looted'
            ];

            if (!validActivityTypes.includes(activity.type)) {
              throw new Error(`Invalid activity type. Must be one of: ${validActivityTypes.join(', ')}`);
            }

            const playerStats = await this.dailyStatsService.recordActivity(steamId, activity);
            return { success: true, data: playerStats, message: 'Activity recorded successfully' };
          }, {
            body: t.Object({
              steamId: t.String({ minLength: 17, maxLength: 17 }),
              activity: t.Object({
                id: t.String(),
                type: t.String({
                  enum: [
                    'rocket_shot', 'c4_thrown', 'satchel_thrown', 'explosive_thrown',
                    'resource_gathered', 'structure_built', 'item_crafted',
                    'animal_killed', 'barrel_looted'
                  ]
                }),
                timestamp: t.String(),
                data: t.Optional(t.Object({
                  killer: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  victim: t.Optional(t.Nullable(t.Object({ name: t.String(), id: t.String() }))),
                  weapon: t.Optional(t.Nullable(t.String())),
                  distance: t.Optional(t.Nullable(t.Number())),
                  resourceType: t.Optional(t.Nullable(t.String())),
                  amount: t.Optional(t.Nullable(t.Number()))
                }))
              })
            })
          })
          .get('/daily/:steamId', async ({ params: { steamId }, query }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const { startDate, endDate } = query;
            const dailyStats = await this.dailyStatsService.getDailyStats(steamId, startDate, endDate);
            return { success: true, data: dailyStats, count: dailyStats.length };
          }, {
            params: t.Object({ steamId: t.String({ minLength: 17, maxLength: 17 }) }),
            query: t.Object({ startDate: t.Optional(t.String()), endDate: t.Optional(t.String()) })
          })
          .get('/daily/:steamId/aggregated', async ({ params: { steamId }, query }) => {
            if (!/^\d{17}$/.test(steamId)) {
              throw new Error('Invalid Steam ID format. Must be 17 digits.');
            }

            const days = Math.min(30, Math.max(1, parseInt(query.days || '14')));
            const aggregatedStats = await this.dailyStatsService.getAggregatedStats(steamId, days);
            return { success: true, data: aggregatedStats, period: `Last ${days} days` };
          }, {
            params: t.Object({ steamId: t.String({ minLength: 17, maxLength: 17 }) }),
            query: t.Object({ days: t.Optional(t.String()) })
          })
      );
  }

  async start(port: number): Promise<void> {
    this.app.listen(port);
    console.log(`Player Stats API is running on http://localhost:${port}`);
  }

  getApp(): Elysia {
    return this.app;
  }
}