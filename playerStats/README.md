# Player Stats Discord Bot

A Discord bot that fetches player statistics from Steam and Battlemetrics, showing total Rust hours from Steam and organization-specific server hours from Battlemetrics, plus kill/death statistics from the past 14 days.

## Features

- 🎮 **Steam Integration**: Get total Rust hours from Steam API
- 🖥️ **Organization Servers**: Track playtime on your organization's servers via Battlemetrics
- ⚔️ **Combat Stats**: View kills, deaths, and K/D ratio from the past 14 days
- 📋 **Recent Activity**: Show recent kills and deaths with timestamps
- 🚀 **Fast & Cached**: Intelligent caching to reduce API calls and improve performance
- 💬 **Prefix Commands**: Simple prefix-based commands (no slash commands)

## Setup

### 1. Prerequisites

- [Bun](https://bun.sh/) installed on your system
- A Discord application and bot token
- Battlemetrics server IDs you want to track

### 2. Installation

```bash
# Clone or navigate to the playerStats directory
cd playerStats

# Install dependencies (already done if you see node_modules)
bun install
```

### 3. Configuration

Edit the `config.json` file with your settings:

```json
{
  "discord": {
    "token": "YOUR_BOT_TOKEN_HERE",
    "prefix": "!"
  },
  "steam": {
    "apiKey": "YOUR_STEAM_API_KEY_HERE",
    "rustAppId": "252490"
  },
  "battlemetrics": {
    "orgId": "YOUR_ORG_ID_HERE",
    "apiUrl": "https://api.battlemetrics.com",
    "apiToken": "YOUR_BATTLEMETRICS_API_TOKEN_HERE"
  },
  "settings": {
    "killDeathDays": 14,
    "cacheTimeMinutes": 30,
    "rateLimitMs": 1000
  }
}
```

#### Configuration Options:

- **discord.token**: Your Discord bot token
- **discord.prefix**: Command prefix (default: "!")
- **steam.apiKey**: Your Steam Web API key
- **steam.rustAppId**: Rust's Steam app ID (252490)
- **battlemetrics.orgId**: Your Battlemetrics organization ID
- **battlemetrics.apiToken**: Your Battlemetrics API token (required for organization data)
- **settings.killDeathDays**: Number of days to look back for kill/death stats (default: 14)
- **settings.cacheTimeMinutes**: How long to cache API responses (default: 30)
- **settings.rateLimitMs**: Minimum time between API requests (default: 1000ms)

### 4. Getting Discord Bot Token

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application or select existing one
3. Go to "Bot" section
4. Copy the bot token
5. Enable necessary bot permissions:
   - Send Messages
   - Use Slash Commands
   - Embed Links

### 5. Getting Steam API Key

1. Go to [Steam Web API Key](https://steamcommunity.com/dev/apikey)
2. Register for a Steam Web API key
3. Copy the key to your config

### 6. Getting Battlemetrics Organization ID

1. Go to your organization's Battlemetrics page
2. The org ID is in the URL: `https://www.battlemetrics.com/organizations/XXXXXXX`
3. Copy the number (XXXXXXX) and add it to the config

### 7. Getting Battlemetrics API Token

1. Go to [Battlemetrics Developers](https://www.battlemetrics.com/developers)
2. Create a personal access token
3. Copy the token to your config
4. **Note**: API token is required to access organization server data

### 8. Running the Bot

```bash
# Start the bot
bun run index.ts
```

You should see:
```
✅ Bot is ready! Logged in as YourBot#1234
📊 Monitoring organization: 12345
🎮 Using prefix: !
```

## Usage

### `!stats` Command

Get comprehensive statistics for a player using their Steam ID or profile URL:

```
!stats 76561198123456789
!stats https://steamcommunity.com/id/playerprofile
!stats playerprofile
```

**Example Output:**
```
📊 PlayerName

🎮 Steam Hours          ⚔️ Combat (14 Days)
245.3 hours             Kills: 127
                        Deaths: 89
                        K/D: 1.43

🖥️ EU Main              🖥️ US West             🖥️ AU Sydney
123.5 hours             89.2 hours             32.6 hours

📋 Recent Activity (Last 5)
🔫 Killed PlayerX with AK47 2 hours ago
💀 Killed by PlayerY with Bolt Action Rifle 3 hours ago
🔫 Killed PlayerZ with Thompson 5 hours ago
```

**Features:**
- Each server is displayed as an inline field
- Steam hours show total Rust playtime
- Organization server hours show time on your specific servers
- Combat stats from the past 14 days
- Recent activity with timestamps

## API Rate Limiting

The bot includes intelligent rate limiting and caching:

- **Rate Limiting**: Minimum 1 second between API requests (configurable)
- **Caching**: API responses cached for 30 minutes (configurable)
- **Parallel Requests**: Multiple data types fetched simultaneously when possible

## Error Handling

The bot handles various error scenarios:

- Player not found on Battlemetrics
- API rate limiting
- Network timeouts
- Invalid server IDs
- Discord API errors

## File Structure

```
playerStats/
├── index.ts                 # Main bot file
├── config.json             # Configuration
├── commands/
│   └── stats.ts            # Player stats command
├── services/
│   ├── battlemetrics.ts    # Battlemetrics API service
│   └── steam.ts            # Steam API service
└── README.md               # This file
```

## Troubleshooting

### Bot not responding to commands
- Check if the bot has proper permissions in your Discord server
- Verify the guild ID in config.json matches your Discord server
- Ensure the bot is online and connected

### "Player not found" errors
- Verify the player name spelling
- Check if the player has played on any of your tracked servers
- Some players may not appear in Battlemetrics search

### API errors
- Check your internet connection
- Verify server IDs are correct
- Battlemetrics API may be temporarily unavailable

### Performance issues
- Increase cache time in config.json
- Reduce the number of tracked servers
- Increase rate limit delay

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is for educational and personal use.
