# Player Stats Discord Bot

A Discord bot that fetches player statistics from Battlemetrics, including playtime hours on specific servers and kill/death statistics from the past 14 days.

## Features

- 📊 **Player Statistics**: Get comprehensive player stats from Battlemetrics
- ⏰ **Server Hours**: Track playtime across multiple configured servers
- ⚔️ **Combat Stats**: View kills, deaths, and K/D ratio from the past 14 days
- 🖥️ **Server Info**: Display server names and current player counts
- 📋 **Recent Activity**: Show recent kills and deaths with timestamps
- 🚀 **Fast & Cached**: Intelligent caching to reduce API calls and improve performance

## Setup

### 1. Prerequisites

- [Bun](https://bun.sh/) installed on your system
- A Discord application and bot token
- Battlemetrics server IDs you want to track

### 2. Installation

```bash
# Clone or navigate to the playerStats directory
cd playerStats

# Install dependencies (already done if you see node_modules)
bun install
```

### 3. Configuration

Edit the `config.json` file with your settings:

```json
{
  "discord": {
    "token": "YOUR_BOT_TOKEN_HERE",
    "clientId": "YOUR_CLIENT_ID_HERE", 
    "guildId": "YOUR_GUILD_ID_HERE"
  },
  "battlemetrics": {
    "serverIds": [
      "1234567",
      "2345678", 
      "3456789"
    ],
    "apiUrl": "https://api.battlemetrics.com"
  },
  "settings": {
    "killDeathDays": 14,
    "cacheTimeMinutes": 30,
    "rateLimitMs": 1000
  }
}
```

#### Configuration Options:

- **discord.token**: Your Discord bot token
- **discord.clientId**: Your Discord application client ID
- **discord.guildId**: The Discord server ID where commands will be registered
- **battlemetrics.serverIds**: Array of Battlemetrics server IDs to track
- **settings.killDeathDays**: Number of days to look back for kill/death stats (default: 14)
- **settings.cacheTimeMinutes**: How long to cache API responses (default: 30)
- **settings.rateLimitMs**: Minimum time between API requests (default: 1000ms)

### 4. Getting Discord Bot Token

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application or select existing one
3. Go to "Bot" section
4. Copy the bot token
5. Enable necessary bot permissions:
   - Send Messages
   - Use Slash Commands
   - Embed Links

### 5. Getting Battlemetrics Server IDs

1. Go to your server's Battlemetrics page
2. The server ID is in the URL: `https://www.battlemetrics.com/servers/rust/XXXXXXX`
3. Copy the number (XXXXXXX) and add it to the `serverIds` array

### 6. Running the Bot

```bash
# Start the bot
bun run index.ts
```

You should see:
```
✅ Successfully reloaded application (/) commands.
✅ Bot is ready! Logged in as YourBot#1234
📊 Monitoring X servers
```

## Usage

### `/playerstats` Command

Get comprehensive statistics for a player:

```
/playerstats player:PlayerName
```

**Example Output:**
```
📊 Player Statistics: PlayerName

⏰ Total Hours: 245.3 hours

⚔️ Combat Stats (Last 14 Days)
Kills: 127
Deaths: 89
K/D Ratio: 1.43

🖥️ Server Hours
EU Main (45/200): 123.5h
US West (67/200): 89.2h
AU Sydney (23/200): 32.6h

📋 Recent Activity (Last 5)
🔫 Killed PlayerX with AK47 2 hours ago
💀 Killed by PlayerY with Bolt Action Rifle 3 hours ago
🔫 Killed PlayerZ with Thompson 5 hours ago
```

## API Rate Limiting

The bot includes intelligent rate limiting and caching:

- **Rate Limiting**: Minimum 1 second between API requests (configurable)
- **Caching**: API responses cached for 30 minutes (configurable)
- **Parallel Requests**: Multiple data types fetched simultaneously when possible

## Error Handling

The bot handles various error scenarios:

- Player not found on Battlemetrics
- API rate limiting
- Network timeouts
- Invalid server IDs
- Discord API errors

## File Structure

```
playerStats/
├── index.ts                 # Main bot file
├── config.json             # Configuration
├── commands/
│   └── playerstats.ts      # Player stats command
├── services/
│   ├── battlemetrics.ts    # Battlemetrics API service
│   └── serverInfo.ts       # Server information service
└── README.md               # This file
```

## Troubleshooting

### Bot not responding to commands
- Check if the bot has proper permissions in your Discord server
- Verify the guild ID in config.json matches your Discord server
- Ensure the bot is online and connected

### "Player not found" errors
- Verify the player name spelling
- Check if the player has played on any of your tracked servers
- Some players may not appear in Battlemetrics search

### API errors
- Check your internet connection
- Verify server IDs are correct
- Battlemetrics API may be temporarily unavailable

### Performance issues
- Increase cache time in config.json
- Reduce the number of tracked servers
- Increase rate limit delay

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is for educational and personal use.
