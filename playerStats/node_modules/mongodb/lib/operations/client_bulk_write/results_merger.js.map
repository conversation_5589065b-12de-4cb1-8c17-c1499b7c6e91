{"version": 3, "file": "results_merger.js", "sourceRoot": "", "sources": ["../../../src/operations/client_bulk_write/results_merger.ts"], "names": [], "mappings": ";;;AAAA,6BAA+C;AAG/C,uCAAwD;AAUxD;;GAEG;AACH,MAAM,cAAc,GAAG;IACrB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,CAAC;IAChB,aAAa,EAAE,CAAC;IAChB,YAAY,EAAE,CAAC;IACf,aAAa,EAAE,CAAC;IAChB,YAAY,EAAE,CAAC;IACf,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,SAAS;CACzB,CAAC;AAyCF;;;GAGG;AACH,MAAa,4BAA4B;IAOvC;;OAEG;IACH,MAAM,CAAC,cAAc;QACnB,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,YAAY,OAA+B;QACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG;YACZ,YAAY,EAAE,IAAI;YAClB,aAAa,EAAE,CAAC;YAChB,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,CAAC;YACf,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,CAAC;YACf,aAAa,EAAE,SAAS;YACxB,aAAa,EAAE,SAAS;YACxB,aAAa,EAAE,SAAS;SACzB,CAAC;QAEF,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,GAAG,EAAiC,CAAC;YACrE,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,GAAG,EAA8B,CAAC;YAClE,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,GAAG,EAA8B,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;YACtC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YACxC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YACxC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;YACtC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YACxC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;YACtC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YACxC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YACxC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;SACzC,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,KAAK,CAAC,MAA6B;QACvC,IAAI,uBAAuB,CAAC;QAC5B,IAAI,CAAC;YACH,IAAI,KAAK,EAAE,MAAM,QAAQ,IAAI,MAAM,EAAE,CAAC;gBACpC,4BAA4B;gBAC5B,IAAI,QAAQ,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC;oBACtB,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;wBAChC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;oBACzC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,yFAAyF;oBACzF,0FAA0F;oBAC1F,yFAAyF;oBACzF,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;wBACzB,MAAM,KAAK,GAAG,IAAI,iCAAyB,CAAC;4BAC1C,OAAO,EAAE,4DAA4D;yBACtE,CAAC,CAAC;wBACH,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAAE;4BAC5D,IAAI,EAAE,QAAQ,CAAC,IAAI;4BACnB,OAAO,EAAE,QAAQ,CAAC,MAAM;yBACzB,CAAC,CAAC;wBACH,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;wBAClC,MAAM,KAAK,CAAC;oBACd,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAAE;4BAC3D,IAAI,EAAE,QAAQ,CAAC,IAAI;4BACnB,OAAO,EAAE,QAAQ,CAAC,MAAM;yBACzB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,0BAAsB,EAAE,CAAC;gBAC5C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC5B,uBAAuB,GAAG;oBACxB,aAAa,EAAE,MAAM,CAAC,SAAS;oBAC/B,aAAa,EAAE,MAAM,CAAC,SAAS;oBAC/B,YAAY,EAAE,MAAM,CAAC,QAAQ;oBAC7B,aAAa,EAAE,MAAM,CAAC,SAAS;oBAC/B,YAAY,EAAE,MAAM,CAAC,QAAQ;oBAC7B,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;iBAC5C,CAAC;gBACF,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;oBAC5D,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;wBAChD,IAAI,QAAQ,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC;4BACtB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;wBACzC,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,8CAA8C;YAC9C,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACpB,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;gBACjC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACjC,CAAC;YAED,8BAA8B;YAC9B,IAAI,CAAC,kBAAkB,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;QACtD,CAAC;QAED,yDAAyD;QACzD,IAAI,uBAAuB,EAAE,CAAC;YAC5B,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,iBAA6B,CAAC;YAChF,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC3B,IAAI,EAAE,iBAAiB,CAAC,IAAI;gBAC5B,OAAO,EAAE,iBAAiB,CAAC,MAAM;aAClC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACK,eAAe,CAAC,MAA6B,EAAE,QAAkB;QACvE,oDAAoD;QACpD,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAClD,yBAAyB;QACzB,IAAI,QAAQ,IAAI,SAAS,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAAE;gBACrE,UAAU,EAAE,SAAS,CAAC,QAAQ,CAAC,GAAG;aACnC,CAAC,CAAC;QACL,CAAC;QACD,yBAAyB;QACzB,IAAI,QAAQ,IAAI,SAAS,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAuB;gBACjC,YAAY,EAAE,QAAQ,CAAC,CAAC;gBACxB,aAAa,EAAE,QAAQ,CAAC,SAAS,IAAI,CAAC;gBACtC,yCAAyC;gBACzC,SAAS,EAAE,QAAQ,CAAC,QAAQ,IAAI,IAAI;aACrC,CAAC;YACF,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACtB,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC;YAC5C,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QACjF,CAAC;QACD,yBAAyB;QACzB,IAAI,QAAQ,IAAI,SAAS,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAAE;gBACrE,YAAY,EAAE,QAAQ,CAAC,CAAC;aACzB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,eAAe,CAAC,QAAkB;QACxC,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,QAAQ,CAAC,YAAY,CAAC;QAClD,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,QAAQ,CAAC,YAAY,CAAC;IACpD,CAAC;CACF;AA5LD,oEA4LC"}