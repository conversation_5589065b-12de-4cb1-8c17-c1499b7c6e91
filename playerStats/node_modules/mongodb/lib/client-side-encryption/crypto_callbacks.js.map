{"version": 3, "file": "crypto_callbacks.js", "sourceRoot": "", "sources": ["../../src/client-side-encryption/crypto_callbacks.ts"], "names": [], "mappings": ";;;AAIA,wCAsBC;AAED,gCAOC;AAED,gCAUC;AAGD,oCAYC;AAED,8CAeC;AA/ED,iCAAiC;AAIjC,SAAgB,cAAc,CAC5B,MAA6C,EAC7C,IAAmC;IAEnC,OAAO,UAAU,GAAW,EAAE,EAAU,EAAE,KAAa,EAAE,MAAc;QACrE,IAAI,MAAM,CAAC;QAEX,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC7B,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,OAAO,MAAM,CAAC,MAAM,CAAC;IACvB,CAAC,CAAC;AACJ,CAAC;AAED,SAAgB,UAAU,CAAC,MAAc,EAAE,KAAa;IACtD,IAAI,CAAC;QACH,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,CAAC;IACX,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,UAAU,CAAC,KAAa,EAAE,MAAc;IACtD,IAAI,MAAM,CAAC;IACX,IAAI,CAAC;QACH,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;IAC9D,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,CAAC;IACX,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpB,OAAO,MAAM,CAAC,MAAM,CAAC;AACvB,CAAC;AAGD,SAAgB,YAAY,CAAC,SAA8B;IACzD,OAAO,CAAC,GAAW,EAAE,KAAa,EAAE,MAAc,EAAkB,EAAE;QACpE,IAAI,MAAM,CAAC;QACX,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QACpE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,OAAO,MAAM,CAAC,MAAM,CAAC;IACvB,CAAC,CAAC;AACJ,CAAC;AAED,SAAgB,iBAAiB,CAAC,GAAW,EAAE,KAAa,EAAE,MAAc;IAC1E,IAAI,MAAM,CAAC;IACX,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,yBAAyB,CAAC,CAAC;QAC5D,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAC5B,gCAAgC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,+BAA+B,CACtF,CAAC;QAEF,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvD,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,CAAC;IACX,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpB,OAAO,MAAM,CAAC,MAAM,CAAC;AACvB,CAAC;AAEY,QAAA,oBAAoB,GAAG,cAAc,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;AACvE,QAAA,oBAAoB,GAAG,cAAc,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;AACzE,QAAA,oBAAoB,GAAG,cAAc,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;AACvE,QAAA,oBAAoB,GAAG,cAAc,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;AACzE,QAAA,cAAc,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AACxC,QAAA,cAAc,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC"}