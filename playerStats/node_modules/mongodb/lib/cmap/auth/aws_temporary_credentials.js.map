{"version": 3, "file": "aws_temporary_credentials.js", "sourceRoot": "", "sources": ["../../../src/cmap/auth/aws_temporary_credentials.ts"], "names": [], "mappings": ";;;AAAA,qCAA2E;AAC3E,uCAA4C;AAC5C,uCAAsC;AAEtC,MAAM,gBAAgB,GAAG,sBAAsB,CAAC;AAChD,MAAM,WAAW,GAAG,wBAAwB,CAAC;AAC7C,MAAM,YAAY,GAAG,4CAA4C,CAAC;AAoBlE;;;;GAIG;AACH,MAAsB,8BAA8B;IAGxC,MAAM,KAAK,MAAM;QACzB,8BAA8B,CAAC,OAAO,KAAK,IAAA,+BAAwB,GAAE,CAAC;QACtE,OAAO,8BAA8B,CAAC,OAAO,CAAC;IAChD,CAAC;IAED,MAAM,KAAK,iBAAiB;QAC1B,OAAO,CAAC,CAAC,cAAc,IAAI,8BAA8B,CAAC,MAAM,CAAC,CAAC;IACpE,CAAC;CACF;AAXD,wEAWC;AAED,gBAAgB;AAChB,MAAa,wBAAyB,SAAQ,8BAA8B;IAG1E;;;OAGG;IACH,YAAY,mBAA2C;QACrD,KAAK,EAAE,CAAC;QAER,IAAI,mBAAmB,EAAE,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,IAAY,QAAQ;QAClB,IAAI,cAAc,IAAI,8BAA8B,CAAC,MAAM,EAAE,CAAC;YAC5D,MAAM,8BAA8B,CAAC,MAAM,CAAC,YAAY,CAAC;QAC3D,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QACD,IAAI,EAAE,0BAA0B,GAAG,EAAE,EAAE,UAAU,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC;QACvE,0BAA0B,GAAG,0BAA0B,CAAC,WAAW,EAAE,CAAC;QACtE,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QAEtC,6IAA6I;QAC7I,MAAM,sBAAsB,GAC1B,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,0BAA0B,CAAC,MAAM,KAAK,CAAC,CAAC;QAErE;;;WAGG;QACH,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC;YAC7B,gBAAgB;YAChB,YAAY;YACZ,gBAAgB;YAChB,gBAAgB;YAChB,YAAY;YACZ,cAAc;YACd,cAAc;YACd,YAAY;YACZ,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;SACZ,CAAC,CAAC;QACH;;;;;;WAMG;QACH,MAAM,cAAc,GAClB,0BAA0B,KAAK,UAAU;YACzC,CAAC,0BAA0B,KAAK,QAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;QAE/E,IAAI,CAAC,SAAS;YACZ,sBAAsB,IAAI,cAAc;gBACtC,CAAC,CAAC,8BAA8B,CAAC,MAAM,CAAC,qBAAqB,CAAC;oBAC1D,YAAY,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;iBACrC,CAAC;gBACJ,CAAC,CAAC,8BAA8B,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;QAEpE,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,KAAK,CAAC,cAAc;QAC3B;;;;;;;;;WASG;QACH,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YACpC,OAAO;gBACL,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,eAAe,EAAE,KAAK,CAAC,eAAe;gBACtC,KAAK,EAAE,KAAK,CAAC,YAAY;gBACzB,UAAU,EAAE,KAAK,CAAC,UAAU;aAC7B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,qBAAa,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;CACF;AApGD,4DAoGC;AAED;;;;GAIG;AACH,MAAa,oCAAqC,SAAQ,8BAA8B;IAC7E,KAAK,CAAC,cAAc;QAC3B,qEAAqE;QACrE,sEAAsE;QACtE,IAAI,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,CAAC;YACvD,OAAO,MAAM,IAAA,eAAO,EAClB,GAAG,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,CAC3E,CAAC;QACJ,CAAC;QAED,6CAA6C;QAE7C,cAAc;QACd,MAAM,KAAK,GAAG,MAAM,IAAA,eAAO,EAAC,GAAG,WAAW,mBAAmB,EAAE;YAC7D,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,EAAE,sCAAsC,EAAE,EAAE,EAAE;SACxD,CAAC,CAAC;QAEH,gBAAgB;QAChB,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAO,EAAC,GAAG,WAAW,IAAI,YAAY,EAAE,EAAE;YAC/D,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,EAAE,0BAA0B,EAAE,KAAK,EAAE;SAC/C,CAAC,CAAC;QAEH,uBAAuB;QACvB,MAAM,KAAK,GAAG,MAAM,IAAA,eAAO,EAAC,GAAG,WAAW,IAAI,YAAY,IAAI,QAAQ,EAAE,EAAE;YACxE,OAAO,EAAE,EAAE,0BAA0B,EAAE,KAAK,EAAE;SAC/C,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAhCD,oFAgCC"}