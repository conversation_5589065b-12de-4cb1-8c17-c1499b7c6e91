"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AUTH_MECHS_AUTH_SRC_EXTERNAL = exports.AuthMechanism = void 0;
/** @public */
exports.AuthMechanism = Object.freeze({
    MONGODB_AWS: 'MONGODB-AWS',
    MONGODB_CR: 'MONGODB-CR',
    MONG<PERSON><PERSON>_DEFAULT: 'DEFAULT',
    MONGODB_GSSAPI: 'GSSAPI',
    MONGODB_PLAIN: 'PLAIN',
    MONGODB_SCRAM_SHA1: 'SCRAM-SHA-1',
    MON<PERSON><PERSON>B_SCRAM_SHA256: 'SCRAM-SHA-256',
    MONGODB_X509: 'MONGODB-X509',
    MONGODB_OIDC: 'MONGODB-OIDC'
});
/** @internal */
exports.AUTH_MECHS_AUTH_SRC_EXTERNAL = new Set([
    exports.AuthMechanism.MONGODB_GSSAPI,
    exports.AuthMechanism.MONGODB_AWS,
    exports.AuthMechanism.MONGODB_OIDC,
    exports.AuthMechanism.MONGODB_X509
]);
//# sourceMappingURL=providers.js.map