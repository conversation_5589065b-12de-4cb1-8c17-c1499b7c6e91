const { SlashCommandBuilder } = require('discord.js');
const { UtilMisc } = require('../utils/util');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('force')
        .setDescription('Shows the next force wipe time'),
    
    async execute(interaction) {
        const util = new UtilMisc(interaction.client);
        const forceTime = interaction.client.force;
        
        const message = `The next force wipe takes place ${util.formatTimestamp(forceTime)} - (${util.formatRelativeTimestamp(forceTime)})`;
        
        await interaction.reply({ content: message });
    }
};
