const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { UtilMisc } = require('../utils/util');
const config = require('./../config.json')

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Shows a list of available commands'),
    
    async execute(interaction) {
        const util = new UtilMisc(interaction.client);
        
        // Check if user has staff role
        const member = await interaction.guild.members.fetch(interaction.user.id);
        if (!util.hasStaffRole(member)) {
            return await interaction.reply({
                content: `> **Error**: You don't have the required role to use this command.`,
                ephemeral: true
            });
        }
        
        const embed = new EmbedBuilder()
            .setTitle('**List of Commands**')
            .setDescription([
                '**/create** - create a server wipe / new embed',
                '**/list** - shows current embeds & wipes',
                '**/editembed** <msgid / id> - allows you to change the embed',
                '**/editserver** <id> - allows you to change serverinfo',
                '**/delete** <type> <id> - delete a wipe or embed',
                '**/force** - shows the next force wipe time',
                `**${config.Misc.Bot_Prefix}wipe** - displays all servers and their next wipes (prefix command)`
            ].join('\n'))
            .setColor(interaction.client.embedHex);
        
        await interaction.reply({ embeds: [embed] });
    }
};
