const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ComponentType } = require('discord.js');
const { UtilMisc } = require('../utils/util');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('delete')
        .setDescription('Delete a server or embed')
        .addStringOption(option =>
            option.setName('type')
                .setDescription('What to delete')
                .setRequired(true)
                .addChoices(
                    { name: 'Server', value: 'server' },
                    { name: 'Embed', value: 'embed' }
                ))
        .addStringOption(option =>
            option.setName('id')
                .setDescription('The ID of the server or embed to delete')
                .setRequired(true)),
    
    async execute(interaction) {
        const util = new UtilMisc(interaction.client);
        
        // Check if user has staff role
        const member = await interaction.guild.members.fetch(interaction.user.id);
        if (!util.hasStaff<PERSON><PERSON>(member)) {
            return await interaction.reply({
                content: `> **Error**: You don't have the required role to use this command.`,
                ephemeral: true
            });
        }
        
        const type = interaction.options.getString('type');
        const id = interaction.options.getString('id');
        
        if (type === 'server') {
            await this.deleteServer(interaction, util, id);
        } else if (type === 'embed') {
            await this.deleteEmbed(interaction, util, id);
        }
    },
    
    async deleteServer(interaction, util, id) {
        try {
            const server = await interaction.client.wipes.findByCustom({ _id: id });
            
            if (!server) {
                return await interaction.reply({
                    content: `> **Error**: Invalid server id. Use \`/list\` for a list of ids`,
                    ephemeral: true
                });
            }
            
            // Create confirmation buttons
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_delete')
                        .setLabel('Confirm')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('cancel_delete')
                        .setLabel('Cancel')
                        .setStyle(ButtonStyle.Danger)
                );
            
            const embed = new EmbedBuilder()
                .setTitle('Delete Confirmation')
                .setDescription(`Are you sure you want to delete **${server.ServerInfo.name}**?`)
                .setColor(interaction.client.embedHex);
            
            await interaction.reply({ embeds: [embed], components: [row] });
            
            try {
                const buttonInteraction = await interaction.channel.awaitMessageComponent({
                    filter: (i) => i.user.id === interaction.user.id && (i.customId === 'confirm_delete' || i.customId === 'cancel_delete'),
                    time: 60000,
                    componentType: ComponentType.Button
                });
                
                if (buttonInteraction.customId === 'confirm_delete') {
                    await interaction.client.wipes.delete(server);
                    
                    const confirmEmbed = new EmbedBuilder()
                        .setTitle('Confirmed')
                        .setDescription(`I have deleted **${server.ServerInfo.name}**.`)
                        .setColor(0x00FF00);
                    
                    await buttonInteraction.update({ embeds: [confirmEmbed], components: [] });
                } else {
                    const cancelEmbed = new EmbedBuilder()
                        .setDescription('Cancelling...')
                        .setColor(0xFF0000);
                    
                    await buttonInteraction.update({ embeds: [cancelEmbed], components: [] });
                }
            } catch (error) {
                if (error.code === 'InteractionCollectorError') {
                    await interaction.editReply({
                        content: '> **Error**: You have taken too long to answer',
                        embeds: [],
                        components: []
                    });
                } else {
                    throw error;
                }
            }
            
        } catch (error) {
            console.error('Error deleting server:', error);
            await interaction.followUp({
                content: 'An error occurred while deleting the server.',
                ephemeral: true
            });
        }
    },
    
    async deleteEmbed(interaction, util, id) {
        try {
            const embedData = await interaction.client.embeds.findByCustom({ _id: parseInt(id) });
            
            if (!embedData) {
                return await interaction.reply({
                    content: '> **Error**: Invalid embed id, please use the embed message id',
                    ephemeral: true
                });
            }
            
            // Create confirmation buttons
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_delete_embed')
                        .setLabel('Confirm')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('cancel_delete_embed')
                        .setLabel('Cancel')
                        .setStyle(ButtonStyle.Danger)
                );
            
            const embed = new EmbedBuilder()
                .setTitle('Delete Confirmation')
                .setDescription('Are you sure you want to delete this embed? (This will delete all servers contained inside it)')
                .setColor(interaction.client.embedHex);
            
            await interaction.reply({ embeds: [embed], components: [row] });
            
            try {
                const buttonInteraction = await interaction.channel.awaitMessageComponent({
                    filter: (i) => i.user.id === interaction.user.id && (i.customId === 'confirm_delete_embed' || i.customId === 'cancel_delete_embed'),
                    time: 60000,
                    componentType: ComponentType.Button
                });
                
                if (buttonInteraction.customId === 'confirm_delete_embed') {
                    // Delete the embed from database
                    await interaction.client.embeds.delete(embedData);
                    
                    // Delete all servers in this embed
                    const servers = await interaction.client.wipes.findManyByCustom({ embed_id: embedData._id });
                    for (const server of servers) {
                        await interaction.client.wipes.delete(server);
                    }
                    
                    // Try to delete the Discord message
                    try {
                        const guild = await interaction.client.guilds.fetch(interaction.client.config.Discord_Config.Guild_ID);
                        const channel = await guild.channels.fetch(embedData.channel_id);
                        if (channel) {
                            const message = await channel.messages.fetch(embedData._id);
                            if (message) {
                                await message.delete();
                            }
                        }
                    } catch (error) {
                        // Ignore errors when deleting the message (it might already be deleted)
                        console.log('Could not delete Discord message:', error.message);
                    }
                    
                    const confirmEmbed = new EmbedBuilder()
                        .setTitle('Confirmed')
                        .setDescription('I have deleted that embed.')
                        .setColor(0x00FF00);
                    
                    await buttonInteraction.update({ embeds: [confirmEmbed], components: [] });
                } else {
                    const cancelEmbed = new EmbedBuilder()
                        .setDescription('Cancelling...')
                        .setColor(0xFF0000);
                    
                    await buttonInteraction.update({ embeds: [cancelEmbed], components: [] });
                }
            } catch (error) {
                if (error.code === 'InteractionCollectorError') {
                    await interaction.editReply({
                        content: '> **Error**: You have taken too long to answer',
                        embeds: [],
                        components: []
                    });
                } else {
                    throw error;
                }
            }
            
        } catch (error) {
            console.error('Error deleting embed:', error);
            await interaction.followUp({
                content: 'An error occurred while deleting the embed.',
                ephemeral: true
            });
        }
    }
};
