const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, StringSelectMenuBuilder, ComponentType } = require('discord.js');
const { UtilMisc } = require('../utils/util');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('editembed')
        .setDescription('Edit an embed')
        .addStringOption(option =>
            option.setName('embed_id')
                .setDescription('The message ID of the embed to edit')
                .setRequired(true)),

    async execute(interaction) {
        const util = new UtilMisc(interaction.client);

        // Check if user has staff role
        const member = await interaction.guild.members.fetch(interaction.user.id);
        if (!util.hasStaffRole(member)) {
            return await interaction.reply({
                content: `> **Error**: You don't have the required role to use this command.`,
                ephemeral: true
            });
        }

        const embedId = interaction.options.getString('embed_id');

        try {
            // Try both string and number formats for the ID
            console.log(`Looking for embed with ID: ${embedId} (type: ${typeof embedId})`);

            let embedData = await interaction.client.embeds.findByCustom({ _id: embedId });

            // If not found as string, try as number
            if (!embedData) {
                const numericId = parseInt(embedId);
                console.log(`Trying numeric ID: ${numericId}`);
                embedData = await interaction.client.embeds.findByCustom({ _id: numericId });
            }

            // If still not found, try as BigInt string (for very large Discord IDs)
            if (!embedData) {
                console.log(`Trying BigInt conversion for ID: ${embedId}`);
                embedData = await interaction.client.embeds.findByCustom({ _id: BigInt(embedId) });
            }

            if (!embedData) {
                // Let's also check what embeds exist
                const allEmbeds = await interaction.client.embeds.getAll();
                console.log(`Available embed IDs: ${allEmbeds.map(e => `${e._id} (${typeof e._id})`).join(', ')}`);

                return await interaction.reply({
                    content: `> **Error**: Invalid embed id, please use the embed message id. Searched for: ${embedId}`,
                    ephemeral: true
                });
            }

            // Create select menu for editing options
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('edit_option')
                .setPlaceholder('Select the feature to edit.')
                .addOptions([
                    {
                        label: 'Title',
                        description: 'Edit the embeds title',
                        value: 'title'
                    },
                    {
                        label: 'Description',
                        description: 'Edit the embeds description',
                        value: 'description'
                    },
                    {
                        label: 'Footer',
                        description: 'Edit the embeds footer',
                        value: 'footer'
                    },
                    {
                        label: 'Color',
                        description: 'Edit the embeds color',
                        value: 'color'
                    },
                    {
                        label: 'Thumbnail',
                        description: 'Edit the embeds thumbnail',
                        value: 'thumbnail'
                    }
                ]);

            const row = new ActionRowBuilder().addComponents(selectMenu);

            const embed = new EmbedBuilder()
                .setTitle('Pick something to edit')
                .setColor(interaction.client.embedHex);

            await interaction.reply({ embeds: [embed], components: [row] });

            try {
                const selectInteraction = await interaction.channel.awaitMessageComponent({
                    filter: (i) => i.user.id === interaction.user.id && i.customId === 'edit_option',
                    time: 60000,
                    componentType: ComponentType.StringSelect
                });

                await selectInteraction.deferUpdate();
                const selectedOption = selectInteraction.values[0];

                await this.handleEditOption(interaction, util, embedData, selectedOption);

            } catch (error) {
                if (error.code === 'InteractionCollectorError') {
                    await interaction.editReply({
                        content: '> **Error**: You took too long to answer',
                        embeds: [],
                        components: []
                    });
                } else {
                    throw error;
                }
            }

        } catch (error) {
            console.error('Error in editembed command:', error);
            await interaction.followUp({
                content: 'An error occurred while editing the embed.',
                ephemeral: true
            });
        }
    },

    async handleEditOption(interaction, util, embedData, option) {
        try {
            let changeTo;
            let promptMessage;

            switch (option) {
                case 'title':
                    promptMessage = 'What do you want the new title to be?';
                    break;
                case 'description':
                    promptMessage = 'What do you want the new description to be?';
                    break;
                case 'footer':
                    promptMessage = 'What do you want the new footer to be?';
                    break;
                case 'color':
                    promptMessage = 'What do you want the new color to be? (hex format like #FF0000)';
                    break;
                case 'thumbnail':
                    promptMessage = 'What do you want the new thumbnail to be? Use an imgur or discord link etc';
                    break;
                default:
                    return await interaction.editReply({
                        content: '> **Error**: Invalid option selected',
                        components: []
                    });
            }

            await interaction.editReply({
                content: 'Please respond with your input...',
                embeds: [],
                components: []
            });

            changeTo = await util.getInput(interaction, promptMessage);

            if (!changeTo) {
                return await interaction.followUp({
                    content: '> **Error**: No input provided or timed out',
                    ephemeral: true
                });
            }

            // Update the embed data
            switch (option) {
                case 'title':
                    embedData.title = changeTo;
                    break;
                case 'description':
                    embedData.description = changeTo;
                    break;
                case 'footer':
                    embedData.footer = changeTo;
                    break;
                case 'color':
                    // Parse color (support hex format)
                    let color;
                    if (changeTo.startsWith('#')) {
                        color = parseInt(changeTo.replace('#', '0x'), 16);
                    } else if (changeTo.startsWith('0x')) {
                        color = parseInt(changeTo, 16);
                    } else {
                        // Try to parse as a number
                        color = parseInt(changeTo);
                        if (isNaN(color)) {
                            return await interaction.followUp({
                                content: '> **Error**: Invalid color format. Use hex format like #FF0000 or 0xFF0000',
                                ephemeral: true
                            });
                        }
                    }
                    embedData.color = color;
                    break;
                case 'thumbnail':
                    embedData.thumbnail = changeTo;
                    break;
            }

            // Update in database
            await interaction.client.embeds.updateByCustom({ _id: embedData._id }, embedData);

            await interaction.followUp({
                content: 'Done, I have updated that for you!',
                ephemeral: true
            });

        } catch (error) {
            console.error('Error handling edit option:', error);
            await interaction.followUp({
                content: 'An error occurred while updating the embed.',
                ephemeral: true
            });
        }
    }
};
