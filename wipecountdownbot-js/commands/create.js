const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, ComponentType } = require('discord.js');
const { UtilMisc } = require('../utils/util');
const axios = require('axios');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('create')
        .setDescription('Create a server wipe or new embed'),

    async execute(interaction) {
        const util = new UtilMisc(interaction.client);

        // Check if user has staff role
        const member = await interaction.guild.members.fetch(interaction.user.id);
        if (!util.hasStaffRole(member)) {
            return await interaction.reply({
                content: `> **Error**: You don't have the required role to use this command.`,
                ephemeral: true
            });
        }

        // Create buttons for server or embed selection
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_server')
                    .setLabel('Server')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('create_embed')
                    .setLabel('Embed')
                    .setStyle(ButtonStyle.Danger)
            );

        const embed = new EmbedBuilder()
            .setDescription('What would you like to create? - A new server or embed?')
            .setColor(interaction.client.embedHex);

        await interaction.reply({ embeds: [embed], components: [row] });

        try {
            const buttonInteraction = await interaction.channel.awaitMessageComponent({
                filter: (i) => i.user.id === interaction.user.id && (i.customId === 'create_server' || i.customId === 'create_embed'),
                time: 60000,
                componentType: ComponentType.Button
            });

            if (buttonInteraction.customId === 'create_server') {
                await this.createServer(buttonInteraction, util);
            } else if (buttonInteraction.customId === 'create_embed') {
                await this.createEmbed(buttonInteraction, util);
            }
        } catch (error) {
            if (error.code === 'InteractionCollectorError') {
                await interaction.editReply({ content: '> **Error**: You have taken too long to answer', components: [] });
            } else {
                console.error('Error in create command:', error);
                await interaction.followUp({ content: 'An error occurred while processing your request.', ephemeral: true });
            }
        }
    },

    async createServer(interaction, util) {
        await interaction.deferUpdate();

        // Check if there are any embeds
        const embeds = await interaction.client.embeds.getAll();
        if (embeds.length <= 0) {
            const embed = new EmbedBuilder()
                .setDescription('> **Error**: You must create an embed first!')
                .setColor(interaction.client.embedHex);

            return await interaction.editReply({ embeds: [embed], components: [] });
        }

        // Create select menu for embed selection
        const options = embeds.map(embed => ({
            label: `${embed._id} : ${embed.title}`,
            value: embed._id.toString()
        }));

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('select_embed')
            .setPlaceholder('Select the embed you would like this to be in.')
            .addOptions(options);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.editReply({
            content: 'Which embed would you like this to be in?',
            embeds: [],
            components: [row]
        });

        try {
            const selectInteraction = await interaction.channel.awaitMessageComponent({
                filter: (i) => i.user.id === interaction.user.id && i.customId === 'select_embed',
                time: 60000,
                componentType: ComponentType.StringSelect
            });

            await selectInteraction.deferUpdate();
            const selectedEmbedId = selectInteraction.values[0];

            // Start the server creation process
            await this.collectServerInfo(selectInteraction, util, selectedEmbedId);

        } catch (error) {
            if (error.code === 'InteractionCollectorError') {
                await interaction.editReply({ content: '> **Error**: You took too long to answer', components: [] });
            } else {
                console.error('Error in server creation:', error);
                await interaction.followUp({ content: 'An error occurred during server creation.', ephemeral: true });
            }
        }
    },

    async collectServerInfo(interaction, util, embedId) {
        await interaction.editReply({ content: 'Starting server creation process...', components: [] });

        try {
            // Get server name
            const serverName = await util.getInput(
                interaction,
                'Please provide the server name (Capitals are important)\nExample:\n\n:flag_eu: **__EU 2x Main__** *using bold & underlining is important*'
            );
            if (!serverName) return;

            // Get server IP
            const serverIP = await util.getInput(
                interaction,
                'What is the IP connect for this server?\nExample: `connect eumain.skizzyrust.com:28015`/`connect ************:28015`'
            );
            if (!serverIP) return;

            // Get server info
            const serverInfo = await util.getInput(
                interaction,
                'Please provide any server information\nExample:\n\nMap Wipe Friday @ 5pm BST\nTeam UI : 4 Max\nMap Size : 3700'
            );
            if (!serverInfo) return;

            // Get Battlemetrics ID
            const serverBMID = await util.getInput(
                interaction,
                'What is the battlemetrics ID for this server?\n> Example: **1234143** would be the id [here](https://cdn.discordapp.com/attachments/944601713023795281/973941405128986714/Screenshot_2022-05-11_at_14.35.22.png).'
            );
            if (!serverBMID) return;

            // Validate Battlemetrics ID
            try {
                const response = await axios.get(`https://api.battlemetrics.com/servers/${serverBMID}`);
                if (response.status !== 200) {
                    return await interaction.followUp({
                        content: `> **Error**: \`${serverBMID}\` is not a valid battlemetrics server ID.`,
                        ephemeral: true
                    });
                }
            } catch (error) {
                return await interaction.followUp({
                    content: `> **Error**: \`${serverBMID}\` is not a valid battlemetrics server ID.`,
                    ephemeral: true
                });
            }

            // Get wipe type
            const wipeType = await util.getInput(
                interaction,
                'Is this wipe schedule `monthly`, `biweekly`, `weekly` or `custom` (multiple wipes a week)?'
            );
            if (!wipeType || !['monthly', 'biweekly', 'weekly', 'custom'].includes(wipeType.toLowerCase())) {
                return await interaction.followUp({
                    content: '> **Error**: Invalid type of server. Allowed: `monthly`, `biweekly`, `weekly` or `custom`',
                    ephemeral: true
                });
            }

            // Create server data based on wipe type
            await this.createServerData(interaction, util, {
                serverName,
                serverIP,
                serverInfo,
                serverBMID,
                wipeType: wipeType.toLowerCase(),
                embedId: parseInt(embedId)
            });

        } catch (error) {
            console.error('Error collecting server info:', error);
            await interaction.followUp({ content: 'An error occurred while collecting server information.', ephemeral: true });
        }
    },

    async createServerData(interaction, util, serverData) {
        const { serverName, serverIP, serverInfo, serverBMID, wipeType, embedId } = serverData;

        try {
            if (wipeType === 'monthly') {
                const data = {
                    _id: util.generateRandomId(25),
                    wipe_type: 'monthly',
                    next_wipe_display: 0,
                    last_wipe: 0,
                    ServerInfo: {
                        name: serverName,
                        ip: serverIP,
                        serverinfo: serverInfo,
                        bmid: serverBMID
                    },
                    embed_id: embedId
                };

                await interaction.client.wipes.insert(data);
                await interaction.followUp({
                    content: `Done, I have created a new wipe for "**${data.ServerInfo.name}**"`,
                    ephemeral: true
                });
                return;
            }

            // For non-monthly servers, ask about force wipes
            const wipesForce = await util.getInput(
                interaction,
                'Does the server wipe at force? (or skip it) - `Yes`/`No`'
            );

            if (!wipesForce) return;

            let wipesForceBoolean;
            if (wipesForce.toLowerCase() === 'yes') {
                wipesForceBoolean = true;
            } else if (wipesForce.toLowerCase() === 'no') {
                wipesForceBoolean = false;
            } else {
                return await interaction.followUp({
                    content: `> **Error**: \`${wipesForce}\` is not a valid response. (\`Yes\`/\`No\`)`,
                    ephemeral: true
                });
            }

            if (wipeType === 'biweekly' || wipeType === 'weekly') {
                await this.handleRegularWipe(interaction, util, serverData, wipesForceBoolean);
            } else if (wipeType === 'custom') {
                await this.handleCustomWipe(interaction, util, serverData, wipesForceBoolean);
            }

        } catch (error) {
            console.error('Error creating server data:', error);
            await interaction.followUp({ content: 'An error occurred while creating server data.', ephemeral: true });
        }
    },

    async handleRegularWipe(interaction, util, serverData, wipesForce) {
        const { serverName, serverIP, serverInfo, serverBMID, wipeType, embedId } = serverData;

        const nextWipeInput = await util.getInput(
            interaction,
            `Please provide the epoch timestamp for the **Next Wipe**\n` +
            `Current time: **${Math.floor(Date.now() / 1000)}**\n` +
            `Use [epochconverter.com](https://www.epochconverter.com/) or [unixtimestamp.com](https://www.unixtimestamp.com/) UTC TIME!`
        );

        if (!nextWipeInput) {
            return await interaction.followUp({ content: 'Cancelling...', ephemeral: true });
        }

        let nextWipe;
        if (/^\d+$/.test(nextWipeInput)) {
            nextWipe = new Date(parseInt(nextWipeInput) * 1000);
        } else {
            const timeInSeconds = util.timeConverter(nextWipeInput);
            nextWipe = new Date(Date.now() + (timeInSeconds * 1000));
        }

        if (wipeType === 'biweekly' && nextWipe.getDay() !== 4) { // Thursday is day 4
            return await interaction.followUp({
                content: `> **Error**: \`${nextWipe}\` was not a Thursday.`,
                ephemeral: true
            });
        }

        const nextWipeTimestamp = Math.floor(nextWipe.getTime() / 1000);
        let display;

        if (wipesForce && interaction.client.force < nextWipeTimestamp) {
            display = interaction.client.force;
        } else {
            display = nextWipeTimestamp;
        }

        const data = {
            _id: util.generateRandomId(25),
            wipe_type: wipeType,
            next_wipe_display: display,
            next_wipe: nextWipeTimestamp,
            last_wipe: 0,
            WipesForce: wipesForce,
            ServerInfo: {
                name: serverName,
                ip: serverIP,
                serverinfo: serverInfo,
                bmid: serverBMID
            },
            embed_id: embedId
        };

        await interaction.client.wipes.insert(data);
        await interaction.followUp({
            content: `Done, I have created a new wipe for "**${data.ServerInfo.name}**"`,
            ephemeral: true
        });
    },

    async handleCustomWipe(interaction, util, serverData, wipesForce) {
        const { serverName, serverIP, serverInfo, serverBMID, wipeType, embedId } = serverData;

        const numWipesInput = await util.getInput(
            interaction,
            'How many wipes does this server have a week? eg: `2`, `3`, `4`, `5`'
        );

        if (!numWipesInput) return;

        const numWipes = parseInt(numWipesInput);
        if (isNaN(numWipes) || numWipes < 2) {
            return await interaction.followUp({
                content: '> **Error**: That is not a number or less than 2',
                ephemeral: true
            });
        }

        await interaction.followUp({
            content: 'I will go through each wipe asking you some questions.',
            ephemeral: true
        });

        const wipes = [];
        for (let i = 0; i < numWipes; i++) {
            const nextWipeInput = await util.getInput(
                interaction,
                `Please provide the epoch timestamp for **Wipe ${i + 1}**\n` +
                `Current time: **${Math.floor(Date.now() / 1000)}**\n` +
                `Use [epochconverter.com](https://www.epochconverter.com/) or [unixtimestamp.com](https://www.unixtimestamp.com/)`
            );

            if (!nextWipeInput) {
                return await interaction.followUp({ content: 'Cancelling...', ephemeral: true });
            }

            let nextWipe;
            if (/^\d+$/.test(nextWipeInput)) {
                nextWipe = new Date(parseInt(nextWipeInput) * 1000);
            } else {
                const timeInSeconds = util.timeConverter(nextWipeInput);
                nextWipe = new Date(Date.now() + (timeInSeconds * 1000));
            }

            wipes.push(Math.floor(nextWipe.getTime() / 1000));
        }

        const data = {
            _id: util.generateRandomId(25),
            wipe_type: wipeType,
            next_wipe_display: 0,
            wipes: wipes,
            last_wipe: 0,
            WipesForce: wipesForce,
            ServerInfo: {
                name: serverName,
                ip: serverIP,
                serverinfo: serverInfo,
                bmid: serverBMID
            },
            embed_id: embedId
        };

        await interaction.client.wipes.insert(data);
        await interaction.followUp({
            content: `Done, I have created a new wipe for "**${data.ServerInfo.name}**"`,
            ephemeral: true
        });
    },

    async createEmbed(interaction, util) {
        await interaction.deferUpdate();

        const channelId = await util.getInput(
            interaction,
            'What is the channel id of the channel you want this embed in?'
        );

        if (!channelId) return;

        try {
            const guild = await interaction.client.guilds.fetch(interaction.client.config.Discord_Config.Guild_ID);
            const channel = await guild.channels.fetch(channelId);

            if (!channel) {
                return await interaction.followUp({
                    content: `> **Error**: I was unable to fetch that channel. <#${channelId}>`,
                    ephemeral: true
                });
            }

            const embed = new EmbedBuilder()
                .setDescription(`Waiting to be edited, \`/editembed\``)
                .setColor(interaction.client.embedHex);

            const message = await channel.send({ embeds: [embed] });

            await interaction.client.embeds.insert({
                _id: message.id,
                channel_id: channel.id,
                title: 'Set with /editembed',
                description: 'Set with /editembed',
                footer: 'Set with /editembed',
                thumbnail: 'https://cdn.discordapp.com/attachments/860941782887039007/911970910439895050/ss.png',
                color: interaction.client.embedHex
            });

            const resultEmbed = new EmbedBuilder()
                .setDescription(`[All Done!](${message.url}) , to edit this embed use \`/editembed ${message.id}\``)
                .setColor(interaction.client.embedHex);

            await interaction.editReply({ embeds: [resultEmbed], components: [] });

        } catch (error) {
            console.error('Error creating embed:', error);
            await interaction.followUp({
                content: `> **Error**: I was unable to fetch that channel. <#${channelId}>`,
                ephemeral: true
            });
        }
    }
};
