const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { UtilMisc } = require('../utils/util');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('list')
        .setDescription('Shows current embeds & wipes'),

    async execute(interaction) {
        const util = new UtilMisc(interaction.client);

        // Check if user has staff role
        const member = await interaction.guild.members.fetch(interaction.user.id);
        if (!util.hasStaffRole(member)) {
            return await interaction.reply({
                content: `> **Error**: You don't have the required role to use this command.`,
                ephemeral: true
            });
        }

        await interaction.deferReply();

        try {
            const serverList = [];
            const allServers = await interaction.client.wipes.getAll();
            const embeds = await interaction.client.embeds.getAll();

            if (embeds.length === 0) {
                return await interaction.editReply({
                    content: '> **Error**: No embeds exist :('
                });
            }

            // Process each embed
            for (const embedData of embeds) {
                const embed = new EmbedBuilder()
                    .setTitle(embedData.title)
                    .setDescription(embedData.description)
                    .setColor(embedData.color)
                    .setFooter({ text: embedData.footer })
                    .setThumbnail(embedData.thumbnail);

                const servers = await interaction.client.wipes.findManyByCustom({ embed_id: embedData._id });

                for (const server of servers) {
                    if (!serverList.includes(server._id)) {
                        serverList.push(server._id);
                    }

                    let name = server.ServerInfo.name + await util.getPopFromBmid(server.ServerInfo.bmid);

                    const now = Math.floor(Date.now() / 1000);
                    if (now - server.last_wipe <= 86400) { // Less than a day
                        name += ` — JUST WIPED: ${util.formatRelativeTimestamp(server.last_wipe)}`;
                    }

                    const value = [
                        `IP: \`${server.ServerInfo.ip}\``,
                        `Next Wipe: ${util.formatTimestamp(server.next_wipe_display)} - (${util.formatRelativeTimestamp(server.next_wipe_display)})`,
                        server.ServerInfo.serverinfo,
                        `Server ID: \`${server._id}\``
                    ].join('\n');

                    embed.addFields({
                        name: name,
                        value: value,
                        inline: false
                    });
                }

                const channelMention = `<#${embedData.channel_id}>`;
                const embedUrl = `https://discord.com/channels/${interaction.client.user.id}/${embedData.channel_id}/${embedData._id}`;
                const content = `${channelMention} Embed ID: ${embedData._id} ${embedUrl}`;

                await interaction.followUp({
                    content: content,
                    embeds: [embed]
                });
            }

            // Process servers not in any embed
            for (const server of allServers) {
                if (!serverList.includes(server._id)) {
                    let name = server.ServerInfo.name + await util.getPopFromBmid(server.ServerInfo.bmid);

                    const now = Math.floor(Date.now() / 1000);
                    if (now - server.last_wipe <= 86400) { // Less than a day
                        name += ` — JUST WIPED: ${util.formatRelativeTimestamp(server.last_wipe)}`;
                    }

                    const value = [
                        `IP: \`${server.ServerInfo.ip}\``,
                        `Next Wipe: ${util.formatTimestamp(server.next_wipe_display)} - (${util.formatRelativeTimestamp(server.next_wipe_display)})`,
                        server.ServerInfo.serverinfo,
                        `Server ID: \`${server._id}\``
                    ].join('\n');

                    await interaction.followUp({
                        content: `${name}\n${value}`
                    });
                }
            }

            if (embeds.length > 0) {
                await interaction.editReply({
                    content: 'Listed all embeds and servers above.'
                });
            }

        } catch (error) {
            console.error('Error in list command:', error);
            await interaction.editReply({
                content: 'An error occurred while fetching the list.'
            });
        }
    }
};
