const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder, ComponentType } = require('discord.js');
const { UtilMisc } = require('../utils/util');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('editserver')
        .setDescription('Edit a server')
        .addStringOption(option =>
            option.setName('server_id')
                .setDescription('The ID of the server to edit')
                .setRequired(true)),
    
    async execute(interaction) {
        const util = new UtilMisc(interaction.client);
        
        // Check if user has staff role
        const member = await interaction.guild.members.fetch(interaction.user.id);
        if (!util.hasStaffRole(member)) {
            return await interaction.reply({
                content: `> **Error**: You don't have the required role to use this command.`,
                ephemeral: true
            });
        }
        
        const serverId = interaction.options.getString('server_id');
        
        try {
            const wipe = await interaction.client.wipes.findByCustom({ _id: serverId });
            
            if (!wipe) {
                return await interaction.reply({
                    content: `> **Error**: Invalid server id. Use \`/list\` for a list of ids`,
                    ephemeral: true
                });
            }
            
            // Create select menu options
            const options = [
                {
                    label: 'Name',
                    description: 'Edit the servers name',
                    value: 'name'
                },
                {
                    label: 'Info',
                    description: 'Edit the servers info',
                    value: 'info'
                },
                {
                    label: 'IP Connect',
                    description: 'Edit the IP Connect',
                    value: 'ip'
                },
                {
                    label: 'BmID',
                    description: 'Edit the servers bmid',
                    value: 'bmid'
                },
                {
                    label: 'Embed its in',
                    description: 'Edit which embed the server is in',
                    value: 'embed'
                }
            ];
            
            // Add wipe time option for weekly/biweekly servers
            if (wipe.wipe_type === 'weekly' || wipe.wipe_type === 'biweekly') {
                options.push({
                    label: 'When\'s wipe',
                    description: 'Edit the wipetime for the server',
                    value: 'wipe_time'
                });
            }
            
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('edit_server_option')
                .setPlaceholder('Select what you wish to edit.')
                .addOptions(options);
            
            const row = new ActionRowBuilder().addComponents(selectMenu);
            
            const embed = new EmbedBuilder()
                .setTitle('Pick something to edit')
                .setColor(interaction.client.embedHex);
            
            await interaction.reply({ embeds: [embed], components: [row] });
            
            try {
                const selectInteraction = await interaction.channel.awaitMessageComponent({
                    filter: (i) => i.user.id === interaction.user.id && i.customId === 'edit_server_option',
                    time: 60000,
                    componentType: ComponentType.StringSelect
                });
                
                await selectInteraction.deferUpdate();
                const selectedOption = selectInteraction.values[0];
                
                await this.handleEditOption(interaction, util, wipe, selectedOption);
                
            } catch (error) {
                if (error.code === 'InteractionCollectorError') {
                    await interaction.editReply({
                        content: '> **Error**: You took too long to answer',
                        embeds: [],
                        components: []
                    });
                } else {
                    throw error;
                }
            }
            
        } catch (error) {
            console.error('Error in editserver command:', error);
            await interaction.followUp({
                content: 'An error occurred while editing the server.',
                ephemeral: true
            });
        }
    },
    
    async handleEditOption(interaction, util, wipe, option) {
        try {
            let changeTo;
            let promptMessage;
            
            switch (option) {
                case 'name':
                    promptMessage = 'What do you want the new servername to be?';
                    break;
                case 'info':
                    promptMessage = 'What do you want the new server description to be?';
                    break;
                case 'ip':
                    promptMessage = 'What do you want the new server IP connect to be?';
                    break;
                case 'bmid':
                    promptMessage = 'What do you want the new bmid to be?';
                    break;
                case 'embed':
                    promptMessage = 'What is the message id of the embed you want the server to be in?';
                    break;
                case 'wipe_time':
                    promptMessage = `Please provide the epoch timestamp for the **Next Wipe**\n` +
                        `Current time: **${Math.floor(Date.now() / 1000)}**\n` +
                        `Use [epochconverter.com](https://www.epochconverter.com/) or [unixtimestamp.com](https://www.unixtimestamp.com/) UTC TIME!`;
                    break;
                default:
                    return await interaction.editReply({
                        content: '> **Error**: Invalid option selected',
                        components: []
                    });
            }
            
            await interaction.editReply({
                content: 'Please respond with your input...',
                embeds: [],
                components: []
            });
            
            changeTo = await util.getInput(interaction, promptMessage);
            
            if (!changeTo) {
                return await interaction.followUp({
                    content: '> **Error**: No input provided or timed out',
                    ephemeral: true
                });
            }
            
            // Update the server data
            switch (option) {
                case 'name':
                    wipe.ServerInfo.name = changeTo;
                    break;
                case 'info':
                    wipe.ServerInfo.serverinfo = changeTo;
                    break;
                case 'ip':
                    wipe.ServerInfo.ip = changeTo;
                    break;
                case 'bmid':
                    wipe.ServerInfo.bmid = changeTo;
                    break;
                case 'embed':
                    try {
                        const embedId = parseInt(changeTo);
                        const embedData = await interaction.client.embeds.findByCustom({ _id: embedId });
                        if (!embedData) {
                            return await interaction.followUp({
                                content: '> **Error**: Invalid embed ID',
                                ephemeral: true
                            });
                        }
                        wipe.embed_id = embedId;
                    } catch (error) {
                        return await interaction.followUp({
                            content: '> **Error**: Invalid embed ID',
                            ephemeral: true
                        });
                    }
                    break;
                case 'wipe_time':
                    try {
                        let nextWipe;
                        if (/^\d+$/.test(changeTo)) {
                            nextWipe = new Date(parseInt(changeTo) * 1000);
                        } else {
                            const timeInSeconds = util.timeConverter(changeTo);
                            nextWipe = new Date(Date.now() + (timeInSeconds * 1000));
                        }
                        
                        if (wipe.wipe_type === 'biweekly' && nextWipe.getDay() !== 4) { // Thursday is day 4
                            return await interaction.followUp({
                                content: `> **Error**: \`${nextWipe}\` was not a Thursday.`,
                                ephemeral: true
                            });
                        }
                        
                        const nextWipeTimestamp = Math.floor(nextWipe.getTime() / 1000);
                        let display;
                        
                        if (wipe.WipesForce && interaction.client.force < nextWipeTimestamp) {
                            display = interaction.client.force;
                        } else {
                            display = nextWipeTimestamp;
                        }
                        
                        wipe.next_wipe = nextWipeTimestamp;
                        wipe.next_wipe_display = display;
                    } catch (error) {
                        return await interaction.followUp({
                            content: '> **Error**: Invalid timestamp format',
                            ephemeral: true
                        });
                    }
                    break;
            }
            
            // Update in database
            await interaction.client.wipes.update(wipe);
            
            await interaction.followUp({
                content: `Done, I have updated **${wipe.ServerInfo.name}**!`,
                ephemeral: true
            });
            
        } catch (error) {
            console.error('Error handling edit option:', error);
            await interaction.followUp({
                content: 'An error occurred while updating the server.',
                ephemeral: true
            });
        }
    }
};
