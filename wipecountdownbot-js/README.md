# Wipe Countdown Bot - JavaScript Version

A Discord bot for managing Rust server wipe countdowns, converted from Python to JavaScript with Bun runtime. This bot uses slash commands for most functionality while keeping the `wipe` command as a prefix command.

## Features

- **Slash Commands**: Modern Discord slash commands for all admin functions
- **Prefix Command**: The `!wipe` command remains as a prefix command for easy access
- **Server Management**: Track multiple Rust servers with different wipe schedules
- **Embed Management**: Create and manage custom embeds for server information
- **Automatic Updates**: Real-time updates of server information and wipe countdowns
- **Battlemetrics Integration**: Automatic population data from Battlemetrics API
- **Force Wipe Support**: Automatic handling of monthly force wipes

## Commands

### Slash Commands (Staff Only)
- `/help` - Shows list of available commands
- `/create` - Create a new server or embed
- `/list` - Shows current embeds and wipes
- `/editembed <embed_id>` - Edit an existing embed
- `/editserver <server_id>` - Edit server information
- `/delete <type> <id>` - Delete a server or embed
- `/force` - Shows the next force wipe time

### Prefix Commands
- `!wipe` - Displays all servers and their next wipes (available to everyone)

## Installation

### Prerequisites
- [Bun](https://bun.sh/) runtime installed
- MongoDB database
- Discord bot token
- Discord server with appropriate permissions

### Setup

1. **Clone/Download the bot files**
   ```bash
   cd wipecountdownbot-js
   ```

2. **Install dependencies**
   ```bash
   bun install
   ```

3. **Configure the bot**
   Edit `config.json` with your settings:
   ```json
   {
     "Misc": {
       "Bot_Prefix": "!",
       "Bot_Token": "YOUR_BOT_TOKEN_HERE",
       "Server_Name": "Your Server Name",
       "Embed_Hex_Color": "#FFFFFF"
     },
     "Mongo_Config": {
       "MONGO_URL": "mongodb://localhost:27017/rust_wipes"
     },
     "Discord_Config": {
       "Guild_ID": "YOUR_GUILD_ID",
       "StaffRole_ID": "YOUR_STAFF_ROLE_ID"
     }
   }
   ```

4. **Start the bot**
   ```bash
   bun start
   ```

   For development with auto-restart:
   ```bash
   bun run dev
   ```

## Configuration

### Required Settings

- **Bot_Token**: Your Discord bot token from the Discord Developer Portal
- **MONGO_URL**: MongoDB connection string
- **Guild_ID**: The Discord server ID where the bot will operate
- **StaffRole_ID**: Role ID that can use admin commands

### Optional Settings

- **Bot_Prefix**: Prefix for the wipe command (default: "!")
- **Server_Name**: Name displayed in wipe embeds
- **Embed_Hex_Color**: Default color for embeds (hex format)

## Usage

### Creating a Server

1. Use `/create` command
2. Select "Server" option
3. Choose which embed to add the server to
4. Follow the prompts to enter:
   - Server name (with formatting)
   - IP connect string
   - Server information
   - Battlemetrics ID
   - Wipe schedule type (monthly/biweekly/weekly/custom)
   - Additional settings based on wipe type

### Creating an Embed

1. Use `/create` command
2. Select "Embed" option
3. Provide the channel ID where the embed should be posted
4. Use `/editembed` to customize the embed appearance

### Managing Servers

- Use `/list` to see all servers and embeds
- Use `/editserver <id>` to modify server settings
- Use `/delete server <id>` to remove a server

### Managing Embeds

- Use `/editembed <message_id>` to edit embed properties
- Use `/delete embed <message_id>` to remove an embed and all its servers

## Wipe Schedule Types

### Monthly
- Automatically follows Rust force wipe schedule
- Updates to next force wipe date automatically

### Weekly
- Wipes every 7 days from the set date
- Can be configured to respect force wipes

### Biweekly
- Wipes every 14 days, must be on Thursdays
- Can be configured to respect force wipes

### Custom
- Multiple wipes per week
- Specify each wipe time individually
- Can be configured to respect force wipes

## Background Tasks

The bot runs two background tasks:

1. **Embed Task** (every 30 seconds): Updates all embed messages with current server information
2. **Server Task** (every 10 seconds): Calculates next wipe times and handles wipe transitions

## Database Structure

The bot uses MongoDB with two collections:

- **wipes**: Server information and wipe schedules
- **embeds**: Embed configuration and Discord message IDs

## Troubleshooting

### Common Issues

1. **Bot not responding to slash commands**
   - Ensure the bot has been invited with the `applications.commands` scope
   - Check that Guild_ID is correct in config.json

2. **Permission errors**
   - Verify StaffRole_ID is correct
   - Ensure users have the specified role

3. **Database connection issues**
   - Check MongoDB connection string
   - Ensure MongoDB is running and accessible

4. **Battlemetrics API errors**
   - Verify server IDs are correct
   - Check internet connectivity

### Logs

The bot provides detailed console logging for debugging:
- Command executions
- Database operations
- API calls
- Error messages

## Migration from Python Version

If migrating from the Python version:

1. Export your MongoDB data
2. Update config.json format (mostly the same)
3. Install and configure the JavaScript version
4. Import your data
5. Test all functionality

The database structure is compatible between versions.

## Support

For issues or questions:
1. Check the console logs for error messages
2. Verify configuration settings
3. Test with a simple setup first
4. Check Discord permissions and bot scope
