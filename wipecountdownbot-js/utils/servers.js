const moment = require('moment-timezone');

let serverTaskInterval;

async function startServerTask(client) {
    console.log('Starting server update task...');

    // Run immediately, then every 10 seconds
    await updateServers(client);

    serverTaskInterval = setInterval(async () => {
        try {
            await updateServers(client);
        } catch (error) {
            console.error('Error in server task:', error);
        }
    }, 10000); // 10 seconds
}

async function updateServers(client) {
    try {
        const now = Math.floor(Date.now() / 1000);
        client.force = getForceTime();
        const allServers = await client.wipes.getAll();

        for (const server of allServers) {
            let updated = false;

            // Monthly (Force Wipe)
            if (server.wipe_type === 'monthly') {
                // Check for new wipe
                if (now > server.next_wipe_display) {
                    server.last_wipe = server.next_wipe_display;
                    server.next_wipe_display = client.force;
                    updated = true;
                }

                // Reset wipe to force?
                if (server.next_wipe_display !== client.force) {
                    server.next_wipe_display = client.force;
                    updated = true;
                }
            }
            // Anything but monthly
            else {
                if (server.wipe_type === 'weekly') {
                    if (now > server.next_wipe) {
                        server.next_wipe += 604800; // 7 days
                        updated = true;
                    }
                } else if (server.wipe_type === 'biweekly') {
                    let nextWipe = server.next_wipe;
                    while ((nextWipe - now) <= 0) {
                        const gap = 14 * 24 * 60 * 60; // 2 weeks in seconds
                        nextWipe = nextWipe + gap;
                        server.next_wipe = nextWipe;
                        updated = true;
                    }
                } else if (server.wipe_type === 'custom') {
                    const wipes = [];
                    for (let wipe of server.wipes) {
                        if (now > wipe) {
                            wipe += 604800; // Add 7 days
                            updated = true;
                        }
                        wipes.push(wipe);
                    }
                    server.wipes = wipes;
                }

                // Display wipe vs actual cycle (Force overriding)
                if (server.wipe_type === 'weekly' || server.wipe_type === 'biweekly') {
                    // Wipe is in past -> New wipe
                    if (now > server.next_wipe_display) {
                        server.last_wipe = server.next_wipe_display;

                        // If time until force is less than 24h OR Force is the next wipe due AND Force is enabled
                        if (((client.force - server.next_wipe < 86400) || (client.force < server.next_wipe)) && server.WipesForce) {
                            server.next_wipe_display = client.force;
                        } else {
                            server.next_wipe_display = server.next_wipe;
                        }
                        updated = true;
                    }
                } else if (server.wipe_type === 'custom') {
                    if (now > server.next_wipe_display) {
                        server.last_wipe = server.next_wipe_display;

                        server.wipes.sort((a, b) => a - b);
                        const smallest = server.wipes[0];

                        // If time until force is less than 24h OR Force is the next wipe due AND Force is enabled
                        if (((client.force - smallest < 86400) || (client.force < smallest)) && server.WipesForce) {
                            server.next_wipe_display = client.force;
                        } else {
                            server.next_wipe_display = smallest;
                        }
                        updated = true;
                    }
                }
            }

            if (updated) {
                await client.wipes.update(server);
            }
        }
    } catch (error) {
        console.error('Error in updateServers:', error);
    }
}

function checkUkGmtBst() {
    const londonTime = moment.tz('Europe/London');
    return londonTime.isDST() ? 'BST' : 'GMT';
}

function getForceTime() {
    let month = moment.utc().startOf('month');
    const now = moment.utc().unix();

    while (true) {
        // Start at 0 on Monday
        const day = month.day();

        // First day of month
        if (day === 1) { // Monday
            month = month.date(4);
        } else if (day === 2) { // Tuesday
            month = month.date(3);
        } else if (day === 3) { // Wednesday
            month = month.date(2);
        } else if (day === 4) { // Thursday
            // Keep as is
        } else if (day === 5) { // Friday
            month = month.date(7);
        } else if (day === 6) { // Saturday
            month = month.date(6);
        } else if (day === 0) { // Sunday
            month = month.date(5);
        } else {
            throw new Error('Invalid day calculation');
        }

        let sHour = 19;
        const gmtBst = checkUkGmtBst();
        if (gmtBst === 'GMT') {
            sHour = 19;
        } else if (gmtBst === 'BST') {
            // BST = UTC/GMT + 1 so must take away an hour
            sHour = 18;
        }
        month = month.hour(sHour).minute(0).second(0);

        if ((month.unix() - now) > 0) {
            // We have a valid timestamp
            break;
        }

        month = month.add(1, 'month').startOf('month');
    }

    return month.unix();
}

function stopServerTask() {
    if (serverTaskInterval) {
        clearInterval(serverTaskInterval);
        serverTaskInterval = null;
        console.log('Server task stopped');
    }
}

module.exports = { startServerTask, stopServerTask };
