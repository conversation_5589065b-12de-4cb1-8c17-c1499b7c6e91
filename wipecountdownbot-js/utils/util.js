const { EmbedBuilder } = require('discord.js');
const axios = require('axios');

class UtilMisc {
    constructor(client) {
        this.client = client;
    }

    // Send a basic embed
    async sendBasicEmbed(interaction, description, options = {}) {
        const embed = new EmbedBuilder()
            .setDescription(description)
            .setColor(options.color || this.client.embedHex);

        if (options.containTimestamp !== false) {
            embed.setTimestamp();
        }

        if (options.includeCommandInvoker !== false) {
            embed.setFooter({
                text: interaction.user.displayName,
                iconURL: interaction.user.displayAvatarURL()
            });
        }

        const replyOptions = { embeds: [embed], ...options };
        
        if (interaction.replied || interaction.deferred) {
            return await interaction.followUp(replyOptions);
        } else {
            return await interaction.reply(replyOptions);
        }
    }

    // Get input from user (for slash commands, we'll use modals or follow-up messages)
    async getInput(interaction, description, timeout = 100000) {
        const embed = new EmbedBuilder()
            .setDescription(description)
            .setColor(this.client.embedHex);

        await interaction.followUp({ embeds: [embed] });

        try {
            const filter = (message) => message.author.id === interaction.user.id;
            const collected = await interaction.channel.awaitMessages({
                filter,
                max: 1,
                time: timeout,
                errors: ['time']
            });

            const message = collected.first();
            return message.content;
        } catch (error) {
            await interaction.followUp({ content: '> **Error**: You took too long to respond.', ephemeral: true });
            return null;
        }
    }

    // Time converter (converts strings like "1h30m" to seconds)
    timeConverter(argument) {
        const timeRegex = /((\d{1,5})(h|s|m|d))/g;
        const timeDict = { h: 3600, s: 1, m: 60, d: 86400 };
        const args = argument.toLowerCase();
        
        let matches = [...args.matchAll(timeRegex)];
        if (!matches.length) {
            return 0;
        }

        let time = 0;
        for (const match of matches) {
            const value = parseInt(match[2]);
            const unit = match[3];
            
            if (timeDict[unit]) {
                time += timeDict[unit] * value;
            } else {
                throw new Error(`${unit} is an invalid time key! h|m|s|d are valid arguments`);
            }
        }
        
        return time;
    }

    // Get population info from Battlemetrics API
    async getPopFromBmid(id) {
        try {
            const url = `https://api.battlemetrics.com/servers/${id}`;
            const response = await axios.get(url);
            
            if (response.status !== 200) {
                console.log(`Battlemetrics - Error with status code: ${response.status}`);
                return ` - Server didn't respond... (${response.status})`;
            }

            const data = response.data;
            const connectedPlayers = data.data.attributes.players || 0;
            const maxPlayers = data.data.attributes.maxPlayers || 0;
            const queuedPlayers = data.data.attributes.details?.rust_queued_players || 0;

            if (queuedPlayers > 0) {
                return ` : ${connectedPlayers}/${maxPlayers} Q:${queuedPlayers}`;
            } else {
                return ` : ${connectedPlayers}/${maxPlayers}`;
            }
        } catch (error) {
            console.error('Error fetching Battlemetrics data:', error);
            return ' - Server didn\'t respond...';
        }
    }

    // Check if user has staff role
    hasStaffRole(member) {
        const staffRoleId = this.client.config.Discord_Config.StaffRole_ID;
        return member.roles.cache.has(staffRoleId);
    }

    // Generate random ID
    generateRandomId(length = 25) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // Format timestamp for Discord
    formatTimestamp(timestamp, format = 'F') {
        return `<t:${Math.floor(timestamp)}:${format}>`;
    }

    // Format relative timestamp for Discord
    formatRelativeTimestamp(timestamp) {
        return `<t:${Math.floor(timestamp)}:R>`;
    }
}

module.exports = { UtilMisc };
