const { EmbedBuilder } = require('discord.js');
const { UtilMisc } = require('./util');
const { Wipe, Embed } = require('../models');

let embedTaskInterval;

async function startEmbedTask(client) {
    console.log('Starting embed update task...');

    const util = new UtilMisc(client);

    // Run immediately, then every 30 seconds
    await updateEmbeds(client, util);

    embedTaskInterval = setInterval(async () => {
        try {
            await updateEmbeds(client, util);
        } catch (error) {
            console.error('Error in embed task:', error);
        }
    }, 30000); // 30 seconds
}

async function updateEmbeds(client, util) {
    try {
        const guild = await client.guilds.fetch(client.config.Discord_Config.Guild_ID);
        const embeds = await Embed.find();

        for (const embedData of embeds) {
            await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay

            // Fetch the Discord channel
            let channel;
            try {
                channel = await guild.channels.fetch(embedData.channel_id);
            } catch (error) {
                console.log(`Error: Cannot fetch channel -> ${embedData.channel_id}`);
                continue;
            }

            if (!channel) {
                console.log(`Error: Cannot fetch channel -> ${embedData.channel_id}`);
                continue;
            }

            // Fetch the Discord message or create a new one
            let message;
            let currentEmbedData = embedData;
            try {
                message = await channel.messages.fetch(embedData._id);
                console.log(`Successfully fetched message ${embedData._id} from channel ${embedData.channel_id}`);
            } catch (error) {
                // Message not found, create a new one
                const newEmbed = new EmbedBuilder()
                    .setTitle(embedData.title)
                    .setDescription(embedData.description)
                    .setColor(embedData.color);

                message = await channel.send({ embeds: [newEmbed] });
                console.log(`Created new message ${message.id} in channel ${embedData.channel_id}`);

                // Update in database
                const oldId = embedData._id;

                // Create new embed document with updated ID
                const newEmbedData = new Embed({
                    _id: message.id,
                    channel_id: embedData.channel_id,
                    title: embedData.title,
                    description: embedData.description,
                    footer: embedData.footer,
                    thumbnail: embedData.thumbnail,
                    color: embedData.color
                });

                // Delete old record and save new one
                await Embed.findByIdAndDelete(oldId);
                await newEmbedData.save();

                // Update all servers with new embed_id
                await Wipe.updateMany({ embed_id: oldId }, { embed_id: message.id });

                // Use the new embed data for the rest of this iteration
                currentEmbedData = newEmbedData;

                console.log(`Error: Failed to fetch message -> ${oldId}, created a new one`);
            }

            // Build the embed with server information
            const embed = new EmbedBuilder()
                .setTitle(currentEmbedData.title)
                .setDescription(currentEmbedData.description)
                .setColor(currentEmbedData.color)
                .setFooter({ text: currentEmbedData.footer })
                .setThumbnail(currentEmbedData.thumbnail);

            const servers = await Wipe.find({ embed_id: currentEmbedData._id });

            for (const server of servers) {
                await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay

                let name = server.ServerInfo.name + await util.getPopFromBmid(server.ServerInfo.bmid);

                const now = Math.floor(Date.now() / 1000);
                if (now - server.last_wipe <= 86400) { // Less than a day
                    name += ` — JUST WIPED: ${util.formatRelativeTimestamp(server.last_wipe)}`;
                }

                const value = [
                    `IP: \`${server.ServerInfo.ip}\``,
                    `Next Wipe: ${util.formatTimestamp(server.next_wipe_display)} - (${util.formatRelativeTimestamp(server.next_wipe_display)})`,
                    server.ServerInfo.serverinfo
                ].join('\n');

                embed.addFields({
                    name: name,
                    value: value,
                    inline: false
                });
            }

            // Update the message
            if (channel && message && typeof message.edit === 'function' && message.id) {
                try {
                    console.log(`Updating message ${message.id} with ${servers.length} servers`);
                    await message.edit({ embeds: [embed] });
                    console.log(`Successfully updated message ${message.id}`);
                } catch (error) {
                    if (error.code === 10008) { // Unknown Message
                        console.log('Error: Message not found -> will be regenerated in next run');
                    } else {
                        console.error('Error updating embed:', error);
                    }
                }
            } else {
                console.log(`Skipping message update - invalid message object for embed ${currentEmbedData._id}. Message type: ${typeof message}, has edit: ${message && typeof message.edit}, has id: ${message && message.id}`);
            }
        }
    } catch (error) {
        console.error('Error in updateEmbeds:', error);
    }
}

function stopEmbedTask() {
    if (embedTaskInterval) {
        clearInterval(embedTaskInterval);
        embedTaskInterval = null;
        console.log('Embed task stopped');
    }
}

module.exports = { startEmbedTask, stopEmbedTask };
