class Document {
    constructor(database, collectionName) {
        this.database = database;
        this.collectionName = collectionName;
        this.collection = database.collection(collectionName);
    }

    // Get all documents
    async getAll(filter = {}) {
        try {
            return await this.collection.find(filter).toArray();
        } catch (error) {
            console.error(`Error getting all documents from ${this.collectionName}:`, error);
            return [];
        }
    }

    // Find one document by ID
    async findById(id) {
        try {
            return await this.collection.findOne({ _id: id });
        } catch (error) {
            console.error(`Error finding document by ID in ${this.collectionName}:`, error);
            return null;
        }
    }

    // Find one document by custom filter
    async findByCustom(filter) {
        try {
            return await this.collection.findOne(filter);
        } catch (error) {
            console.error(`Error finding document by custom filter in ${this.collectionName}:`, error);
            return null;
        }
    }

    // Find many documents by custom filter
    async findManyByCustom(filter) {
        try {
            return await this.collection.find(filter).toArray();
        } catch (error) {
            console.error(`Error finding documents by custom filter in ${this.collectionName}:`, error);
            return [];
        }
    }

    // Insert a document
    async insert(data) {
        try {
            const result = await this.collection.insertOne(data);
            return result;
        } catch (error) {
            console.error(`Error inserting document into ${this.collectionName}:`, error);
            throw error;
        }
    }

    // Update a document
    async update(data) {
        try {
            const { _id, ...updateData } = data;
            const result = await this.collection.updateOne(
                { _id: _id },
                { $set: updateData }
            );
            return result;
        } catch (error) {
            console.error(`Error updating document in ${this.collectionName}:`, error);
            throw error;
        }
    }

    // Update by custom filter
    async updateByCustom(filter, data) {
        try {
            const result = await this.collection.updateOne(
                filter,
                { $set: data }
            );
            return result;
        } catch (error) {
            console.error(`Error updating document by custom filter in ${this.collectionName}:`, error);
            throw error;
        }
    }

    // Delete a document
    async delete(data) {
        try {
            const result = await this.collection.deleteOne({ _id: data._id });
            return result;
        } catch (error) {
            console.error(`Error deleting document from ${this.collectionName}:`, error);
            throw error;
        }
    }

    // Delete by ID
    async deleteById(id) {
        try {
            const result = await this.collection.deleteOne({ _id: id });
            return result;
        } catch (error) {
            console.error(`Error deleting document by ID from ${this.collectionName}:`, error);
            throw error;
        }
    }

    // Delete by custom filter
    async deleteByCustom(filter) {
        try {
            const result = await this.collection.deleteMany(filter);
            return result;
        } catch (error) {
            console.error(`Error deleting documents by custom filter from ${this.collectionName}:`, error);
            throw error;
        }
    }

    // Upsert (insert or update)
    async upsert(filter, data) {
        try {
            const result = await this.collection.updateOne(
                filter,
                { $set: data },
                { upsert: true }
            );
            return result;
        } catch (error) {
            console.error(`Error upserting document in ${this.collectionName}:`, error);
            throw error;
        }
    }
}

module.exports = { Document };
