const { Client, GatewayIntentBits, Collection, REST, Routes } = require('discord.js');
const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');
const config = require('./config.json');
const { Document } = require('./utils/mongo');
const { startEmbedTask } = require('./utils/embeds');
const { startServerTask } = require('./utils/servers');

// Create Discord client
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
    ]
});

// Bot properties
client.config = config;
client.embedHex = parseInt(config.Misc.Embed_Hex_Color.replace('#', '0x'), 16);
client.force = 999999999999999999;

// Collections for commands
client.commands = new Collection();
client.slashCommands = new Collection();

// Load slash commands
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

const commands = [];

for (const file of commandFiles) {
    const filePath = path.join(commandsPath, file);
    const command = require(filePath);

    if ('data' in command && 'execute' in command) {
        client.slashCommands.set(command.data.name, command);
        commands.push(command.data.toJSON());
        console.log(`Loaded slash command: ${command.data.name}`);
    } else {
        console.log(`[WARNING] The command at ${filePath} is missing a required "data" or "execute" property.`);
    }
}

// Load prefix commands (only wipe command)
const prefixCommandsPath = path.join(__dirname, 'prefix-commands');
if (fs.existsSync(prefixCommandsPath)) {
    const prefixCommandFiles = fs.readdirSync(prefixCommandsPath).filter(file => file.endsWith('.js'));

    for (const file of prefixCommandFiles) {
        const filePath = path.join(prefixCommandsPath, file);
        const command = require(filePath);

        if ('name' in command && 'execute' in command) {
            client.commands.set(command.name, command);
            console.log(`Loaded prefix command: ${command.name}`);
        }
    }
}

// Bot ready event
client.once('ready', async () => {
    console.log(`We have logged in as ${client.user.tag}\n`);

    // Register slash commands
    const rest = new REST({ version: '10' }).setToken(config.Misc.Bot_Token);

    try {
        console.log('Started refreshing application (/) commands.');

        await rest.put(
            Routes.applicationGuildCommands(client.user.id, config.Discord_Config.Guild_ID),
            { body: commands },
        );

        console.log('Successfully reloaded application (/) commands.');
    } catch (error) {
        console.error(error);
    }

    // Start background tasks
    startEmbedTask(client);
    startServerTask(client);
});

// Handle slash command interactions
client.on('interactionCreate', async interaction => {
    if (!interaction.isChatInputCommand()) return;

    const command = client.slashCommands.get(interaction.commandName);

    if (!command) {
        console.error(`No command matching ${interaction.commandName} was found.`);
        return;
    }

    try {
        await command.execute(interaction);
    } catch (error) {
        console.error(error);
        const reply = { content: 'There was an error while executing this command!', ephemeral: true };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(reply);
        } else {
            await interaction.reply(reply);
        }
    }
});

// Handle prefix commands (for wipe command)
client.on('messageCreate', async message => {
    if (!message.content.startsWith(config.Misc.Bot_Prefix) || message.author.bot) return;

    const args = message.content.slice(config.Misc.Bot_Prefix.length).trim().split(/ +/);
    const commandName = args.shift().toLowerCase();

    const command = client.commands.get(commandName);

    if (!command) return;

    try {
        await command.execute(message, args);
    } catch (error) {
        console.error(error);
        await message.reply('There was an error while executing this command!');
    }
});

// Error handling
client.on('error', console.error);

// Initialize database and start bot
async function main() {
    try {
        // Connect to MongoDB
        const mongoClient = new MongoClient(config.Mongo_Config.MONGO_URL);
        await mongoClient.connect();
        const db = mongoClient.db('rust_wipes');

        // Initialize document collections
        client.wipes = new Document(db, 'wipes');
        client.embeds = new Document(db, 'embeds');

        console.log('Connected to MongoDB');

        // Start the bot
        await client.login(config.Misc.Bot_Token);
    } catch (error) {
        console.error('Failed to start bot:', error);
        process.exit(1);
    }
}

main();
