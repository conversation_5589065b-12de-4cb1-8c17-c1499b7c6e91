const { EmbedBuilder } = require('discord.js');
const { UtilMisc } = require('../utils/util');

module.exports = {
    name: 'wipe',
    description: 'Displays all servers and their next wipes',

    async execute(message, args) {
        const util = new UtilMisc(message.client);

        try {
            const wipes = await message.client.wipes.getAll();

            if (wipes.length === 0) {
                const embed = new EmbedBuilder()
                    .setDescription('> **Error**: No current servers.')
                    .setColor(message.client.embedHex);

                return await message.reply({ embeds: [embed] });
            }

            let description = '';
            for (const wipe of wipes) {
                description += `${wipe.ServerInfo.name}: ${util.formatTimestamp(wipe.next_wipe_display)} - (${util.formatRelativeTimestamp(wipe.next_wipe_display)})\n`;
            }

            // Limit description to 1020 characters (Discord limit is 4096, but original code used 1020)
            if (description.length > 1020) {
                description = description.substring(0, 1020);
            }

            const wipeEmbed = new EmbedBuilder()
                .setTitle(message.client.config.Misc.Server_Name)
                .setDescription(description)
                .setColor(message.client.embedHex)
                .setFooter({ text: 'Automatically displayed in your local timezone!' });

            await message.reply({ embeds: [wipeEmbed] });
        } catch (error) {
            console.error('Error in wipe command:', error);
            await message.reply('There was an error while executing this command!');
        }
    }
};
